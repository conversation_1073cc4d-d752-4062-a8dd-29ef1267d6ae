# 数据结构设计

## 表结构设计

### `chat_history`表，包含如下字段：
  * id: 主键，自增
  * chat_uid: 聊天的唯一id，暂时使用秒级别时间戳
  * platform_id: 平台，比如是deepseek还是chatgpt等等
  * chat_answer: 聊天页面输入提示词对应的回答
  * chat_group_name：聊天组名称，一般位于对话的顶部
  * chat_sort: 有的平台，比如像deeepseek可以获得本次问题在本对话的序号
  * p_uid: 从当前平台获得的，本次文词在当前聊天平台的唯一id，有的是session_id+序号
  * create_time 创建时间 取时间戳
  * is_synced: 是否已同步过，0否1是，默认0
  * is_delete: 是否删除，0否1是，默认0
  * is_answered: number

索引：
chat_uid 与 platform_id组成联合唯一索引，不能重复

### 表`chat_prompt`
  * id: 主键，自增
  * chat_uid: 聊天的唯一id，暂时使用秒级别时间戳
  * chat_prompt: 聊天页面输入框的提示词
  * tags: 标签，数组类型 暂时为空
  * create_time 创建时间 取时间戳
  * is_synced: 是否已同步过，0否1是，默认0
  * is_delete: 是否删除，0否1是，默认0

索引：
chat_uid 为唯一索引，不能重复

### 表`platform` 作为不同平台的字典表，存储如下字段。
  * id 主键，自增
  * name: 平台名称，比如deepseek
  * url: 平台的url，比如https://chat.deepseek.com
  * icon: 平台的faviconl路径,表示网站的图标
  * icon_base64: 平台图标的base64编码，用于展示
  * is_delete: 是否删除，0否1是，默认0 

索引：
name 为唯一索引，不能重复

