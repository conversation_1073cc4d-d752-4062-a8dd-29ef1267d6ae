---
type: "development_rules"
description: "命名规范和代码风格规则"
---

# 命名规范和代码风格

## 文件命名规则

### 类和组件命名
- **类文件**: PascalCase，如 `ClassName.ts`
- **React组件**: PascalCase，如 `ComponentName.tsx`
- **工具文件**: camelCase，如 `utilityName.ts`
- **配置文件**: camelCase，如 `configName.ts`
- **类型文件**: `types.ts` 或 `TypeName.ts`

### 目录命名
- **模块目录**: camelCase，如 `moduleName/`
- **组件目录**: 小写复数，如 `components/`
- **工具目录**: 小写复数，如 `utils/`

### 特殊文件命名
- **平台适配器**: `platformName.ts` (如 `chatgpt.ts`)
- **注入器**: `ComponentNameInject.ts` (如 `FloatingBubbleInject.ts`)
- **捕捉器**: `CaptureName.ts` (如 `InputCapture.ts`)
- **服务类**: `ServiceName.ts` (如 `ChatHistoryService.ts`)
- **DAO类**: `EntityNameDao.ts` (如 `ChatHistoryDao.ts`)

## 变量和函数命名

### 变量命名
- **普通变量**: camelCase，如 `userName`, `messageCount`
- **常量**: UPPER_SNAKE_CASE，如 `MAX_RETRY_COUNT`, `DEFAULT_TIMEOUT`
- **私有属性**: 下划线前缀，如 `_internalState`
- **布尔变量**: is/has/can前缀，如 `isVisible`, `hasPermission`, `canEdit`

### 函数命名
- **普通函数**: camelCase，动词开头，如 `getUserData()`, `sendMessage()`
- **事件处理**: handle前缀，如 `handleClick()`, `handleSubmit()`
- **获取器**: get前缀，如 `getSelectors()`, `getConfig()`
- **设置器**: set前缀，如 `setVisible()`, `setConfig()`
- **检查器**: is/has/can前缀，如 `isReady()`, `hasPermission()`

### 类和接口命名
- **类名**: PascalCase，名词，如 `MessageHandler`, `DatabaseConnection`
- **接口名**: PascalCase，通常以I前缀或Type后缀，如 `IAdapter`, `ConfigType`
- **抽象类**: Abstract前缀，如 `AbstractAdapter`, `BaseCapture`
- **枚举**: PascalCase，如 `MessageType`, `PlatformType`

## 导入导出规则

### 导入顺序
1. **Node.js 内置模块** - 如 `fs`, `path`
2. **第三方库** - 如 `React`, `clsx`
3. **项目内部模块** - 使用 `@/` 别名
4. **相对路径导入** - 如 `./styles.css`

### 导出规则
- **优先使用命名导出** - 便于重构和树摇
- **默认导出仅用于主要组件** - React组件等
- **避免混合导出** - 一个文件主要使用一种导出方式

### 导入示例
```typescript
// 1. Node.js 内置模块
import path from 'path';

// 2. 第三方库
import React from 'react';
import clsx from 'clsx';

// 3. 项目内部模块
import { MessageType } from '@/common/types';
import { MessagingService } from '@/common/service';

// 4. 相对路径导入
import './styles.css';
```

## 注释规范

### 文件头注释
```typescript
/**
 * 文件功能描述
 * <AUTHOR>
 * @date 创建日期
 */
```

### 类注释
```typescript
/**
 * 类功能描述
 * @description 详细描述类的职责和使用场景
 * @example 使用示例
 */
class ClassName {
}
```

### 方法注释
```typescript
/**
 * 方法功能描述
 * @param paramName 参数描述
 * @returns 返回值描述
 * @throws 可能抛出的异常
 */
methodName(paramName: string): ReturnType {
}
```

### 行内注释
- 复杂逻辑必须有行内注释说明
- 注释要说明"为什么"而不是"是什么"
- 使用中文注释，简洁明确

## TypeScript 类型命名

### 类型定义
- **接口**: PascalCase + Type后缀，如 `ConfigType`, `MessageType`
- **类型别名**: PascalCase，如 `UserData`
- **泛型参数**: 单个大写字母，如 `T`, `K`, `V`
- **枚举**: PascalCase，如 `MessageType`, `PlatformType`

### 类型文件组织
- **实体类型**: `database_entity.ts`
- **VO类型**: `content_vo.ts`, `comm_vo.ts`
- **枚举类型**: `enums.ts`
- **通用类型**: `index.ts`

## 常量命名规范

### 配置常量
```typescript
// 平台配置
const PLATFORM_CONFIG = {
  CHATGPT: 'chatgpt',
  CLAUDE: 'claude'
} as const;

// 超时设置
const TIMEOUT_CONFIG = {
  DEFAULT: 5000,
  LONG: 30000
} as const;
```

### 选择器常量
```typescript
// CSS选择器
const SELECTORS = {
  INPUT_FIELD: 'textarea[data-id="root"]',
  SEND_BUTTON: 'button[data-testid="send-button"]'
} as const;
```

### 消息类型常量
```typescript
// 消息类型枚举
enum MessageType {
  DB_CHAT_PROMPT_CREATE = 'DB_CHAT_PROMPT_CREATE',
}
```

## 代码风格规范

### 缩进和格式
- 使用 2 个空格缩进
- 行末不留空格
- 文件末尾保留一个空行
- 使用 Prettier 自动格式化

### 语句规范
- 语句末尾必须加分号
- 字符串优先使用单引号
- 对象和数组最后一个元素后加逗号
- 使用 const 和 let，避免 var

### 函数定义
```typescript
// 箭头函数（推荐）
const handleClick = (event: MouseEvent) => {
  // 处理逻辑
};

// 普通函数
function processData(data: DataType): ResultType {
  // 处理逻辑
  return result;
}
```
