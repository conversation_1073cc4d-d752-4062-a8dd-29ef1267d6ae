/**
 * SelectorManager 改造验证测试
 * 用于验证选择器统一管理改造是否成功
 */

import SelectorManager from "./SelectorManager";
import { kimiSelector } from "./kimiConfig";
import { CommonSelectors } from "./CommonSelectors";
import { KimiConfig } from "../types/Consts";

/**
 * 测试选择器管理器改造
 */
export class SelectorManagerTest {
    
    /**
     * 测试选择器初始化和获取
     */
    public static testSelectorInitialization(): boolean {
        try {
            console.log('[SelectorManagerTest] 开始测试选择器初始化');
            
            // 初始化选择器
            const selector = SelectorManager.initSelector(KimiConfig);
            
            // 验证基础选择器存在
            if (!selector.inputField || !selector.sendButton) {
                console.error('[SelectorManagerTest] 基础选择器缺失');
                return false;
            }
            
            // 验证 Kimi 特有选择器存在
            if (!selector.chatContentList || !selector.answerItem || !selector.copyButton) {
                console.error('[SelectorManagerTest] Kimi 特有选择器缺失');
                return false;
            }
            
            console.log('[SelectorManagerTest] 选择器配置:', selector);
            console.log('[SelectorManagerTest] 选择器初始化测试通过');
            return true;
            
        } catch (error) {
            console.error('[SelectorManagerTest] 选择器初始化测试失败:', error);
            return false;
        }
    }
    
    /**
     * 测试选择器获取方法
     */
    public static testSelectorGetter(): boolean {
        try {
            console.log('[SelectorManagerTest] 开始测试选择器获取方法');
            
            // 先初始化
            SelectorManager.initSelector(KimiConfig);
            
            // 获取选择器
            const selector = SelectorManager.getSelector();
            
            if (!selector) {
                console.error('[SelectorManagerTest] 获取选择器失败');
                return false;
            }
            
            // 验证选择器内容
            console.log('[SelectorManagerTest] 输入框选择器数量:', selector.inputField?.length);
            console.log('[SelectorManagerTest] 发送按钮选择器数量:', selector.sendButton?.length);
            console.log('[SelectorManagerTest] 复制按钮选择器数量:', selector.copyButton?.length);
            
            console.log('[SelectorManagerTest] 选择器获取测试通过');
            return true;
            
        } catch (error) {
            console.error('[SelectorManagerTest] 选择器获取测试失败:', error);
            return false;
        }
    }
    
    /**
     * 测试选择器合并逻辑
     */
    public static testSelectorMerging(): boolean {
        try {
            console.log('[SelectorManagerTest] 开始测试选择器合并逻辑');
            
            // 初始化选择器
            const mergedSelector = SelectorManager.initSelector(KimiConfig);
            
            // 验证平台选择器优先级
            const kimiInputSelectors = kimiSelector.inputField || [];
            const commonInputSelectors = CommonSelectors.inputField || [];
            
            // 检查合并后的选择器是否包含平台和通用选择器
            const mergedInputSelectors = mergedSelector.inputField || [];
            
            // 验证平台选择器在前
            let platformSelectorsFound = 0;
            for (let i = 0; i < kimiInputSelectors.length; i++) {
                if (mergedInputSelectors[i] === kimiInputSelectors[i]) {
                    platformSelectorsFound++;
                }
            }
            
            if (platformSelectorsFound !== kimiInputSelectors.length) {
                console.error('[SelectorManagerTest] 平台选择器优先级验证失败');
                return false;
            }
            
            console.log('[SelectorManagerTest] 选择器合并逻辑测试通过');
            return true;
            
        } catch (error) {
            console.error('[SelectorManagerTest] 选择器合并逻辑测试失败:', error);
            return false;
        }
    }
    
    /**
     * 运行所有测试
     */
    public static runAllTests(): boolean {
        console.log('[SelectorManagerTest] 开始运行所有测试');
        
        const tests = [
            this.testSelectorInitialization,
            this.testSelectorGetter,
            this.testSelectorMerging
        ];
        
        let passedTests = 0;
        
        for (const test of tests) {
            if (test.call(this)) {
                passedTests++;
            }
        }
        
        const allPassed = passedTests === tests.length;
        
        console.log(`[SelectorManagerTest] 测试完成: ${passedTests}/${tests.length} 通过`);
        
        if (allPassed) {
            console.log('[SelectorManagerTest] 🎉 所有测试通过！选择器管理器改造成功！');
        } else {
            console.error('[SelectorManagerTest] ❌ 部分测试失败，需要检查改造');
        }
        
        return allPassed;
    }
}

// 如果在浏览器环境中，可以直接运行测试
if (typeof window !== 'undefined') {
    // 延迟执行，确保模块加载完成
    setTimeout(() => {
        SelectorManagerTest.runAllTests();
    }, 1000);
}
