---
type: "development_rules"
description: "UI组件开发规则和规范"
---

# UI组件开发规范

## 技术栈
- **样式**: Tailwind CSS 3 + shadcn/ui
- **框架**: React 18 + TypeScript 5

## 组件分类和位置

### 可复用组件
- **位置**: `extension/src/components/`
- **用途**: 跨模块使用的基础UI组件（图标、按钮、卡片等）

### Content Script UI组件
- **位置**: `extension/src/content/components/`
- **特点**: 原生DOM操作，注入到网页中
- **用途**: 浮动小球、存档按钮等页面注入组件

### Popup/Options React组件
- **位置**: `extension/src/popup/components/` 或 `extension/src/options/components/`
- **特点**: 基于React框架
- **用途**: 弹窗和设置页面的UI组件

## Content Script UI组件规范

### 基础结构要求
```typescript
class ComponentName {
  private element: HTMLElement | null = null;
  private isRendered: boolean = false;
  private eventListeners: Array<{element: Element, event: string, handler: Function}> = [];

  render(): HTMLElement { /* 渲染逻辑 */ }
  destroy(): void { /* 清理资源 */ }
}
```

### 必须实现的方法
- **render()**: 渲染组件并返回DOM元素
- **getTemplate()**: 返回HTML模板
- **applyStyles()**: 应用内联样式
- **setupEventListeners()**: 设置事件监听器
- **destroy()**: 销毁组件和清理资源

### 事件处理规范
- 记录所有事件监听器用于清理
- 使用 `echosync:` 前缀命名自定义事件
- 在 destroy() 中清理所有事件监听器

## React UI组件规范

### 基础结构
```typescript
interface ComponentProps {
  className?: string;
  children?: React.ReactNode;
  // 其他属性
}

const ComponentName: React.FC<ComponentProps> = ({ 
  className, 
  children,
  ...props 
}) => {
  return (
    <div className={clsx('base-styles', className)} {...props}>
      {children}
    </div>
  );
};
```

### 组件设计原则
- 使用 TypeScript 定义 Props 接口
- 支持 className 和 children 属性透传
- 使用 clsx 库处理条件样式
- 提供合理的默认值

### 状态管理
- 优先使用 useState 管理本地状态
- 使用 useEffect 处理副作用
- 状态提升到合适的父组件

## 样式管理规范

### CSS类命名
- 使用 `echosync-` 前缀避免样式冲突
- 组件类: `echosync-floating-bubble`
- 状态类: `echosync-bubble--hover`
- 尺寸类: `echosync-icon--sm`

### 样式应用策略
- **Content Script**: 优先使用内联样式，设置最高 z-index (2147483647)
- **React组件**: 使用 Tailwind CSS 类名
- **条件样式**: 使用 clsx 库处理

### 响应式设计
- 移动端(<768px)、平板(768-1024px)、桌面(>1024px)
- 优先考虑移动端体验
- 实现 adjustForViewport() 方法（Content Script组件）

## 可访问性规范

### ARIA属性
- 设置合适的 role 属性
- 提供 aria-label 描述
- 支持 tabindex 键盘导航

### 键盘导航
- 监听 keydown 事件
- 支持 Enter 和 Space 键激活
- 提供焦点样式反馈

## 性能优化

### 事件处理
- 对频繁触发的事件使用防抖
- 对滚动事件使用节流
- 及时清理事件监听器

### 内存管理
- 避免内存泄漏
- 清理定时器和异步操作
- 使用 WeakMap 存储临时数据

### 长列表优化
- 超过50项的列表使用虚拟化
- 只渲染可见区域的项目