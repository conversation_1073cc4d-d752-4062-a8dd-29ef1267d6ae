---
type: "agent_requested"
description: "Content Script数据模型开发规则"
---
# Content Script数据模型规则

## 模块职责
Model层负责管理Content Script模块间的共享数据状态，提供统一的数据访问接口，避免模块间的直接数据传递。

## 核心设计原则

### 简单可靠
- **继承Singleton**: Model类继承Singleton<T>基类实现单例
- **构造函数注入**: 通过构造函数参数传递依赖
- **显式获取**: 通过ClassName.getInstance()获取单例实例

## 项目结构规范

### 文件组织
- **Model实现**: `extension/src/content/model/`
- **命名**: `ModelName.ts`


## Model开发规范

### Singleton Model实现
- 继承Singleton基类实现单例
- 构造函数中调用super()
- 实现业务逻辑方法
- 提供数据访问接口

### 使用构造函数注入
- 通过构造函数参数传递依赖
- 依赖声明为私有属性
- 在适配器中获取单例并注入