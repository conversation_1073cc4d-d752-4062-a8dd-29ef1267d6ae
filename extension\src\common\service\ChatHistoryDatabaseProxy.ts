import { Singleton } from "../base/Singleton"
import { DatabaseResult } from "../types/comm_vo"
import { CreateChatHistoryReq, CreateChatHistoryResp } from "../types/content_vo"
import { MessageType } from "../types/enums"
import { MessagingService } from "./MessagingService"

/**
 * 聊天历史数据库代理服务
 * 负责chat_history表的操作
 */
export class ChatHistoryDatabaseProxy extends Singleton<ChatHistoryDatabaseProxy> {

  /**
   * 创建聊天历史记录
   */
  async createChatHistory(input: CreateChatHistoryReq): Promise<DatabaseResult<CreateChatHistoryResp>> {
    try {
      const response = await MessagingService.sendToBackground(MessageType.DB_CHAT_HISTORY_CREATE, input)
      return response
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Create chat history failed'
      }
    }
  }

  /**
   * 更新聊天历史记录
   */
  async updateChatHistory(promptUid: string, updates: Partial<CreateChatHistoryReq>): Promise<DatabaseResult<boolean>> {
    try {
      const response = await MessagingService.sendToBackground(MessageType.DB_CHAT_HISTORY_UPDATE, {
        prompt_uid: promptUid,
        ...updates
      })
      return response
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Update chat history failed'
      }
    }
  }
}

// 导出单例实例
export const chatHistoryDatabaseProxy = ChatHistoryDatabaseProxy.getInstance()