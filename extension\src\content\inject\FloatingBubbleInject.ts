import { FloatingBubble } from '@/content/inject/components/FloatingBubble'
import { HistoryBubbleService } from '@/content/inject/HistoryBubbleService'
import { EchoSyncEventEnum } from '../types/DOMEnum' // EchoSync 自定义事件枚举
import { FloatingBubbleDragService } from './FloatingBubbleDragService'
import { BaseAIAdapter } from '../adapters/BaseAIAdapter'
import { ChatPromptListResp,  } from '@/common/types/content_vo'
import {DatabaseResult} from '@/common/types/comm_vo'
import { chatPromptDatabaseProxy } from '@/common/service/ChatPromptDatabaseProxy'

/**
 * 悬浮气泡注入逻辑
 * 处理事件监听、业务逻辑和与其他组件的交互
 */
export class FloatingBubbleInject {
  private component: FloatingBubble
  private historyBubbleService: HistoryBubbleService  = null
  private floatingBubbleDragService: FloatingBubbleDragService | null = null
  private adapter: BaseAIAdapter
  private hoverTimer: number | null = null

  constructor(adapter: BaseAIAdapter) {
    this.adapter = adapter
    this.inject()
    this.setupEventListeners()
    this.initializeHistoryBubble()
  }

  /**
   * 注入到页面
   */
  async inject(): Promise<void> {
    this.component = new FloatingBubble()
    const bubble = this.component.render()
    
    if (bubble) {
      // 添加到DOM
      document.body.appendChild(bubble)
      
      // 初始化拖拽处理
      this.floatingBubbleDragService = new FloatingBubbleDragService(bubble)
      ;(bubble as any).floatingBubbleDragService = this.floatingBubbleDragService
      
      // 设置气泡事件监听
      this.setupBubbleEventListeners()
      
      console.log('【EchoSync】Floating bubble injected successfully')
    }
  }

  /**
   * 设置全局事件监听
   */
  private setupEventListeners(): void {
    // 监听输入框聚焦事件
    document.addEventListener(EchoSyncEventEnum.INPUT_FOCUSED, (event: any) => {
      console.log('【EchoSync】FloatingBubbleInject received INPUT_FOCUSED event:', event.detail)
      this.handleInputFocused(event.detail)
    })

    // 监听页面变化事件
    document.addEventListener(EchoSyncEventEnum.PAGE_CHANGED, () => {
      this.handlePageChanged()
    })

    // 监听边界回弹事件
    document.addEventListener(EchoSyncEventEnum.SNAP_TO_BOUNDARY, () => {
      this.snapToBoundary()
    })

    // 监听窗口大小变化
    this.setupWindowResizeListener()
  }

  /**
   * 设置气泡DOM事件监听
   */
  private setupBubbleEventListeners(): void {
    const bubble = this.component.getElement()
    if (!bubble) return

    // 鼠标悬停事件
    bubble.addEventListener('mouseenter', this.handleMouseEnter.bind(this))
    bubble.addEventListener('mouseleave', this.handleMouseLeave.bind(this))

    // 点击事件
    bubble.addEventListener('click', this.handleClick.bind(this))

    // 右键点击事件
    bubble.addEventListener('contextmenu', this.handleContextMenu.bind(this))
  }

  /**
   * 处理鼠标悬停进入
   */
  private handleMouseEnter(): void {
    this.component.setHoverEffect(true)
    console.log('【EchoSync】Mouse enter floating bubble')
    
    // 延迟显示历史气泡
    this.hoverTimer = window.setTimeout(() => {
      this.showHistoryBubble()
    }, 500)
  }

  /**
   * 处理鼠标悬停离开
   */
  private handleMouseLeave(): void {
    this.component.setHoverEffect(false)
    
    // 清除定时器
    if (this.hoverTimer) {
      clearTimeout(this.hoverTimer)
      this.hoverTimer = null
    }

    // 延迟隐藏历史气泡
    setTimeout(() => {
      if (!this.isMouseOverHistoryBubble()) {
        this.historyBubbleService.hide()
      }
    }, 300)
  }

  /**
   * 处理点击事件
   */
  private handleClick(e: MouseEvent): void {
    // 检查是否是拖拽后的点击
    if (this.floatingBubbleDragService && this.floatingBubbleDragService.isDragging()) {
      e.preventDefault()
      return
    }

    // 发布气泡点击事件
    document.dispatchEvent(new CustomEvent(EchoSyncEventEnum.FLOATING_BUBBLE_CLICKED, {
      detail: { position: { x: e.clientX || 0, y: e.clientY || 0 } }
    }))

    // 触发显示存储的提示词事件
    document.dispatchEvent(new CustomEvent(EchoSyncEventEnum.SHOW_STORED_PROMPTS))
  }

  /**
   * 处理右键点击事件
   */
  private handleContextMenu(e: Event): void {
    e.preventDefault()
    document.dispatchEvent(new CustomEvent(EchoSyncEventEnum.DEBUG_FEATURES))
  }

  /**
   * 处理输入框聚焦
   */
  private handleInputFocused(data: { element: HTMLElement }): void {
    if (!data || !data.element) {
      console.error('【EchoSync】Invalid data in handleInputFocused:', data);
      return;
    }

    // 检查组件是否存在
    if (!this.component) {
      console.error('【EchoSync】FloatingBubble component is null in handleInputFocused, reinitializing...');
      this.reinitializeBubble();
      if (!this.component) {
        console.error('【EchoSync】Failed to reinitialize FloatingBubble component');
        return;
      }
    }

    // 检查气泡元素是否存在
    const bubbleElement = this.component.getElement();
    if (!bubbleElement || !document.body.contains(bubbleElement)) {
      console.warn('【EchoSync】Bubble element missing, reinitializing...');
      this.reinitializeBubble();
      // 再次检查是否初始化成功
      if (!this.component || !this.component.getElement()) {
        console.error('【EchoSync】Failed to reinitialize bubble element');
        return;
      }
    }

    // 安全调用 moveToInputField
    try {
      this.component.moveToInputField(data.element);
      // console.log('【EchoSync】Floating bubble moved to input field on focus');
    } catch (error) {
      console.error('【EchoSync】Error moving bubble to input field:', error);
      // 尝试重新初始化并再次移动
      this.reinitializeBubble();
      if (this.component) {
        try {
          this.component.moveToInputField(data.element);
        } catch (retryError) {
          console.error('【EchoSync】Retry failed:', retryError);
        }
      }
    }
  }

  /**
   * 处理页面变化
   */
  private handlePageChanged(): void {
    // 页面变化时重新定位气泡
    setTimeout(() => {
      const inputElement = this.adapter.getInputCapture().getInputElement()
      if (inputElement) {
        this.component.moveToInputField(inputElement)
        console.log('【EchoSync】Floating bubble repositioned after page change')
      }
    }, 200)
  }

  /**
   * 设置窗口大小变化监听
   */
  private setupWindowResizeListener(): void {
    let resizeTimeout: NodeJS.Timeout
    // 监听resize事件
    window.addEventListener('resize', () => {
      clearTimeout(resizeTimeout)
      resizeTimeout = setTimeout(() => {
        const inputElement = this.adapter.getInputCapture().getInputElement()
        if (inputElement) {
          this.component.moveToInputField(inputElement)
          console.log('【EchoSync】Floating bubble repositioned after window resize')
        }
      }, 300)
    })
  }

  /**
   * 重新初始化气泡组件
   */
  private reinitializeBubble(): void {
    try {
      console.log('【EchoSync】Attempting to reinitialize floating bubble...');
      
      // 清理旧的气泡
      if (this.component) {
        try {
          this.component.destroy();
        } catch (error) {
          console.warn('【EchoSync】Error destroying old bubble:', error);
        }
      }
      
      // 清理旧的拖拽处理
      if (this.floatingBubbleDragService) {
        try {
          this.floatingBubbleDragService.destroy();
        } catch (error) {
          console.warn('【EchoSync】Error destroying old drag service:', error);
        }
        this.floatingBubbleDragService = null;
      }
      
      // 创建新的气泡
      this.component = new FloatingBubble();
      const bubble = this.component.render();
      
      if (bubble && document.body) {
        // 从 DOM 中移除可能存在的旧气泡
        const existingBubbles = document.querySelectorAll('[class*="floating-bubble"]');
        existingBubbles.forEach(oldBubble => {
          if (oldBubble !== bubble && oldBubble.parentNode) {
            oldBubble.remove();
          }
        });
        
        document.body.appendChild(bubble);
        
        // 重新初始化拖拽处理
        this.floatingBubbleDragService = new FloatingBubbleDragService(bubble);
        (bubble as any).floatingBubbleDragService = this.floatingBubbleDragService;
        
        // 重新设置事件监听
        this.setupBubbleEventListeners();
        
        console.log('【EchoSync】Floating bubble reinitialized successfully');
      } else {
        console.error('【EchoSync】Failed to create bubble element or document.body not available');
        this.component = null;
      }
    } catch (error) {
      console.error('【EchoSync】Error reinitializing bubble:', error);
      this.component = null;
    }
  }

  /**
   * 初始化历史气泡
   */
  private initializeHistoryBubble(): void {
    this.historyBubbleService = new HistoryBubbleService({
      maxItems: 10,
      showPlatformIcons: true
    })

    // 监听历史项点击事件
    document.addEventListener(EchoSyncEventEnum.HISTORY_ITEM_CLICK, (event: any) => {
      const { chat } = event.detail
      document.dispatchEvent(new CustomEvent(EchoSyncEventEnum.HANDLE_HISTORY_CLICK, { detail: { chat } }))
    })
  }

  /**
   * 显示历史气泡
   */
  private async showHistoryBubble(): Promise<void> {
    // 直接调用加载历史数据
    await this.loadHistoryData()

    // 直接调用显示历史气泡
    this.displayHistoryBubble()
  }

  /**
   * 加载历史数据
   */
  private async loadHistoryData(): Promise<void> {
      // 获取历史数据
      const result: DatabaseResult<ChatPromptListResp[]> = await chatPromptDatabaseProxy.getChatPromptList({ limit: 10 })
      if (result.success && result.data) {
        console.log('【FloatingBubbleInject】History data loaded:', result.data.length, 'items')
        // 更新历史气泡数据
        if (this.historyBubbleService) {
          this.historyBubbleService.updateHistory(result.data)
        }
      } else {
        console.error('【FloatingBubbleInject】Failed to load history data:', result.error)
      }
  }

  /**
   * 显示历史气泡
   */
  private displayHistoryBubble(): void {
    if (!this.historyBubbleService) return

    console.log('【FloatingBubbleInject】Displaying history bubble')

    // 获取悬浮球元素作为锚点
    const bubbleElement = this.component.getElement()
    if (bubbleElement) {
      // 显示历史气泡
      this.historyBubbleService.show(bubbleElement)
    }
  }

  /**
   * 检查鼠标是否在历史气泡上
   */
  private isMouseOverHistoryBubble(): boolean {
    if (!this.historyBubbleService) return false
    return this.historyBubbleService.visible
  }

  /**
   * 移动到输入框附近
   */
  moveToInputField(inputElement: HTMLElement): void {
    this.component.moveToInputField(inputElement)
  }

  /**
   * 移动到默认位置
   */
  moveToDefaultPosition(): void {
    this.component.moveToDefaultPosition()
  }

  /**
   * 边界回弹
   */
  snapToBoundary(): void {
    this.component.snapToBoundary()
  }

  /**
   * 销毁
   */
  destroy(): void {
    // 清理定时器
    if (this.hoverTimer) {
      clearTimeout(this.hoverTimer)
      this.hoverTimer = null
    }
    //清理监听
    document.removeEventListener(EchoSyncEventEnum.INPUT_FOCUSED, (event: any) => {
      this.handleInputFocused(event.detail)
    })
    document.removeEventListener(EchoSyncEventEnum.PAGE_CHANGED, () => {
      this.handlePageChanged()
    })
    document.removeEventListener(EchoSyncEventEnum.SNAP_TO_BOUNDARY, () => {
      this.snapToBoundary()
    })
    window.removeEventListener('resize', () => {
      this.setupWindowResizeListener()
    })

    // 销毁组件
    this.component.destroy()
    this.historyBubbleService.destroy()
    this.floatingBubbleDragService?.destroy()

    
    console.log('【EchoSync】FloatingBubbleInject destroyed')
  }
}
