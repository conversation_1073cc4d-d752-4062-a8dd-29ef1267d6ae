---
type: "development_rules"
description: "Popup模块开发规则"
---

# Popup模块开发规则

## 模块职责
Popup模块提供Chrome插件的弹出窗口界面，是用户与插件交互的主要入口。

## 核心设计原则

### 模块特点
- **快速访问**: 提供插件核心功能的快速入口
- **轻量级**: 界面简洁，加载快速
- **实时状态**: 显示插件当前状态和统计信息
- **导航中心**: 引导用户到详细功能页面

### 技术架构
- **框架**: React 18 + TypeScript 5
- **样式**: Tailwind CSS 3 + shadcn/ui
- **状态**: Zustand 4 或 React Context
- **路由**: React Router 6（如需要）

## 项目结构规范

### 目录组织
```
popup/
├── components/             # 弹窗专用组件
│   ├── Header.tsx         # 头部组件
│   ├── StatusCard.tsx     # 状态卡片
│   ├── QuickActions.tsx   # 快速操作
│   └── Navigation.tsx     # 导航组件
├── pages/                 # 页面组件
│   ├── Dashboard.tsx      # 主面板
│   ├── Settings.tsx       # 快速设置
│   └── History.tsx        # 历史记录
├── hooks/                 # 自定义Hooks
│   ├── usePopupState.ts   # 弹窗状态管理
│   └── useExtensionData.ts # 插件数据获取
├── App.tsx               # 主应用组件
├── main.tsx              # 入口文件
└── index.html            # HTML模板
```

## 组件开发规范

### 页面组件设计
- **单一职责**: 每个页面组件专注一个功能领域
- **状态管理**: 使用自定义Hooks管理状态
- **数据获取**: 通过消息与Background通信
- **错误处理**: 提供友好的错误提示

### UI组件规范
- 继承通用UI组件规范（参考 02-ui-components.md）
- 适配小尺寸窗口（通常320x600px）
- 支持键盘导航
- 提供加载和错误状态

### 组件示例
```typescript
interface StatusCardProps {
  title: string;
  value: string | number;
  icon?: React.ReactNode;
  loading?: boolean;
}

const StatusCard: React.FC<StatusCardProps> = ({ 
  title, 
  value, 
  icon, 
  loading = false 
}) => {
  return (
    <div className="echosync-status-card p-4 border rounded-lg">
      <div className="flex items-center gap-2">
        {icon}
        <span className="text-sm text-gray-600">{title}</span>
      </div>
      <div className="mt-2">
        {loading ? (
          <div className="animate-pulse bg-gray-200 h-6 w-16 rounded"></div>
        ) : (
          <span className="text-2xl font-bold">{value}</span>
        )}
      </div>
    </div>
  );
};
```

## 数据管理规范

### 与Background通信
- 严格遵循 background/database.md 规则
- 使用 MessagingService.sendToBackground 发送请求
- 处理 DatabaseResult 格式的返回数据
- 实现错误处理和重试机制

### 状态管理策略
```typescript
// 使用自定义Hook管理弹窗状态
const usePopupState = () => {
  const [stats, setStats] = useState<StatsType | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchStats = useCallback(async () => {
    try {
      setLoading(true);
      const result = await MessagingService.sendToBackground({
        type: MessageType.GET_STATS,
        payload: {}
      });
      
      if (result.success) {
        setStats(result.data);
      } else {
        setError(result.error);
      }
    } catch (err) {
      setError('获取数据失败');
    } finally {
      setLoading(false);
    }
  }, []);

  return { stats, loading, error, fetchStats };
};
```

### 数据缓存策略
- 缓存频繁访问的数据
- 设置合理的缓存过期时间
- 提供手动刷新功能
- 处理数据同步问题

## 用户体验规范

### 界面设计原则
- **简洁明了**: 突出核心功能，避免信息过载
- **快速响应**: 优化加载时间，提供即时反馈
- **一致性**: 与Options页面保持设计一致
- **可访问性**: 支持键盘导航和屏幕阅读器

### 交互设计
- **快速操作**: 提供常用功能的一键操作
- **状态反馈**: 清晰显示操作结果和进度
- **导航引导**: 引导用户到详细功能页面
- **错误处理**: 友好的错误提示和恢复建议

### 响应式适配
- 适配不同的弹窗尺寸
- 支持内容滚动
- 优化小屏幕显示
- 保持布局稳定性

## 性能优化

### 加载优化
- 延迟加载非关键组件
- 预加载常用数据
- 优化图片和资源大小
- 使用代码分割

### 渲染优化
- 避免不必要的重渲染
- 使用React.memo优化组件
- 合理使用useCallback和useMemo
- 优化列表渲染性能

### 内存管理
- 及时清理事件监听器
- 避免内存泄漏
- 清理定时器和异步操作
- 合理使用useEffect依赖

## 开发检查清单

### 组件开发检查
- [ ] 使用 React + TypeScript 开发
- [ ] 遵循组件命名规范
- [ ] 实现响应式布局
- [ ] 支持键盘导航
- [ ] 添加加载和错误状态
- [ ] 文件大小不超过 300 行

### 数据管理检查
- [ ] 遵循 database.md 规则进行数据操作
- [ ] 使用 MessagingService 与Background通信
- [ ] 处理 DatabaseResult 格式返回数据
- [ ] 实现错误处理和重试机制
- [ ] 添加数据缓存策略

### 用户体验检查
- [ ] 界面简洁明了
- [ ] 加载时间优化
- [ ] 提供状态反馈
- [ ] 支持快速操作
- [ ] 错误提示友好
- [ ] 适配小尺寸窗口

### 性能优化检查
- [ ] 避免不必要的重渲染
- [ ] 使用适当的性能优化技术
- [ ] 及时清理资源
- [ ] 优化加载性能
- [ ] 测试内存使用情况
