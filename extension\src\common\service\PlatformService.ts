import { platformDao } from '../dao/PlatformDao'
import { PlatformEntity } from '@/common/types/database_entity'
import {
  CreatePlatformInput,
  UpdatePlatformInput,
} from '@/common/types/content_vo'

import {PaginatedResult,DatabaseResult} from '@/common/types/comm_vo'

/**
 * 平台业务服务
 * 负责业务逻辑处理和Entity到DTO的转换
 */
export class PlatformService {
  private static instance: PlatformService

  public static getInstance(): PlatformService {
    if (!PlatformService.instance) {
      PlatformService.instance = new PlatformService()
    }
    return PlatformService.instance
  }

  /**
   * 创建平台记录
   */
  async create(input: CreatePlatformInput): Promise<DatabaseResult<PlatformEntity>> {
    try {
      // 检查名称是否已存在
      const nameExists = await platformDao.nameExists(input.name)
      if (nameExists) {
        return {
          success: false,
          error: `Platform name '${input.name}' already exists`
        }
      }

      // 检查URL是否已存在
      const urlExists = await platformDao.urlExists(input.url)
      if (urlExists) {
        return {
          success: false,
          error: `Platform URL '${input.url}' already exists`
        }
      }

      const platform = await platformDao.create({
        name: input.name,
        url: input.url,
        icon: input.icon,
        icon_base64: input.icon_base64,
        is_delete: 0
      })

      return {
        success: true,
        data: platform
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 根据ID查找平台记录
   */
  async findById(id: number): Promise<DatabaseResult<PlatformEntity | null>> {
    try {
      const platform = await platformDao.findById(id)
      return {
        success: true,
        data: platform
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 获取所有平台记录
   */
  async findAll(options: {
    includeDeleted?: boolean
    limit?: number
    page?: number
  } = {}): Promise<DatabaseResult<PaginatedResult<PlatformEntity>>> {
    try {
      const { includeDeleted = false, limit = 50, page = 1 } = options
      const offset = (page - 1) * limit

      const platforms = await platformDao.findAll({
        includeDeleted,
        limit,
        offset
      })

      const total = await platformDao.count({ includeDeleted })

      return {
        success: true,
        data: {
          data: platforms,
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit)
        }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 获取所有活跃的平台记录
   */
  async findAllActive(): Promise<DatabaseResult<PlatformEntity[]>> {
    try {
      const platforms = await platformDao.findAllActive()
      return {
        success: true,
        data: platforms
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 更新平台记录
   */
  async update(id: number, input: UpdatePlatformInput): Promise<DatabaseResult<PlatformEntity>> {
    try {
      // 检查名称是否已存在（排除当前记录）
      if (input.name !== undefined) {
        const nameExists = await platformDao.nameExists(input.name, id)
        if (nameExists) {
          return {
            success: false,
            error: `Platform name '${input.name}' already exists`
          }
        }
      }

      // 检查URL是否已存在（排除当前记录）
      if (input.url !== undefined) {
        const urlExists = await platformDao.urlExists(input.url, id)
        if (urlExists) {
          return {
            success: false,
            error: `Platform URL '${input.url}' already exists`
          }
        }
      }

      const updates: Partial<PlatformEntity> = {}
      if (input.name !== undefined) updates.name = input.name
      if (input.url !== undefined) updates.url = input.url
      if (input.icon !== undefined) updates.icon = input.icon
      if (input.icon_base64 !== undefined) updates.icon_base64 = input.icon_base64
      if (input.is_delete !== undefined) updates.is_delete = input.is_delete

      const updated = await platformDao.update(id, updates)

      return {
        success: true,
        data: updated
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 删除平台记录
   */
  async delete(id: number): Promise<DatabaseResult<boolean>> {
    try {
      const result = await platformDao.softDelete(id)
      return {
        success: true,
        data: result
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 恢复平台记录
   */
  async restore(id: number): Promise<DatabaseResult<boolean>> {
    try {
      const result = await platformDao.restore(id)
      return {
        success: true,
        data: result
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 搜索平台记录
   */
  async search(searchTerm: string, options: {
    limit?: number
    page?: number
  } = {}): Promise<DatabaseResult<PaginatedResult<PlatformEntity>>> {
    try {
      const { limit = 50, page = 1 } = options

      // 搜索平台
      const allPlatforms = await platformDao.search(searchTerm, { limit: 1000 })

      // 分页处理
      const total = allPlatforms.length
      const offset = (page - 1) * limit
      const paginatedPlatforms = allPlatforms.slice(offset, offset + limit)

      return {
        success: true,
        data: {
          data: paginatedPlatforms,
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit)
        }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 更新平台图标
   */
  async updateIcon(id: number, icon?: string, iconBase64?: string): Promise<DatabaseResult<PlatformEntity>> {
    try {
      const updated = await platformDao.updateIcon(id, icon, iconBase64)
      return {
        success: true,
        data: updated
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 获取平台使用统计
   */
  async getUsageStats(): Promise<DatabaseResult<Array<{ platform: PlatformEntity; chatCount: number }>>> {
    try {
      const stats = await platformDao.getUsageStats()
      return {
        success: true,
        data: stats
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 批量创建平台记录
   */
  async bulkCreate(inputs: CreatePlatformInput[]): Promise<DatabaseResult<PlatformEntity[]>> {
    try {
      // 验证所有输入
      for (const input of inputs) {
        const nameExists = await platformDao.nameExists(input.name)
        if (nameExists) {
          return {
            success: false,
            error: `Platform name '${input.name}' already exists`
          }
        }

        const urlExists = await platformDao.urlExists(input.url)
        if (urlExists) {
          return {
            success: false,
            error: `Platform URL '${input.url}' already exists`
          }
        }
      }

      // 批量创建
      const platforms = await platformDao.bulkCreate(inputs.map(input => ({
        name: input.name,
        url: input.url,
        icon: input.icon,
        icon_base64: input.icon_base64
      })))

      return {
        success: true,
        data: platforms
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 获取统计信息
   */
  async getStats(): Promise<DatabaseResult<{
    total: number
    active: number
    deleted: number
  }>> {
    try {
      const total = await platformDao.count({ includeDeleted: true })
      const active = await platformDao.count({ includeDeleted: false })
      const deleted = total - active

      return {
        success: true,
        data: {
          total,
          active,
          deleted
        }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }
}

// 导出单例实例
export const platformService = PlatformService.getInstance()
