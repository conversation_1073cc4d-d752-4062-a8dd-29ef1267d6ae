import React from 'react'
import { SettingSection } from '../components/SettingSection'
import { ToggleSwitch } from '../components/ToggleSwitch'
import { useSettings } from '../hooks/useSettings'

export const PlatformSettings: React.FC = () => {
  const { settings, updateSettings, loading } = useSettings()

  if (loading || !settings) {
    return <div className="animate-pulse">加载中...</div>
  }

  const updatePlatform = (platformId: string, updates: Partial<any>) => {
    const updatedPlatforms = settings.platforms.map(platform =>
      platform.id === platformId ? { ...platform, ...updates } : platform
    )
    updateSettings({ platforms: updatedPlatforms })
  }

  return (
    <div className="space-y-6">
      <SettingSection
        title="平台配置"
        description="管理各个AI平台的集成设置"
      >
        {settings.platforms.map((platform) => (
          <div key={platform.id} className="border rounded-lg p-4">
            <div className="flex items-center justify-between mb-3">
              <h3 className="font-medium text-gray-900">{platform.name}</h3>
              <ToggleSwitch
                label=""
                checked={platform.enabled}
                onChange={(checked) => updatePlatform(platform.id, { enabled: checked })}
              />
            </div>
            
            {platform.enabled && (
              <div className="ml-4 space-y-3">
                <ToggleSwitch
                  label="自动捕捉"
                  description="自动捕捉该平台的对话内容"
                  checked={platform.autoCapture}
                  onChange={(checked) => updatePlatform(platform.id, { autoCapture: checked })}
                />
              </div>
            )}
          </div>
        ))}
      </SettingSection>
    </div>
  )
}