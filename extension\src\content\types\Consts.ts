import { PlatformConfig } from '@/content/types/PlatformConfigType'

/**
 * 通用正则表达式模式
 */
export const CommonRegexPatterns = {
  /** 清理HTML标签 */
  HTML_TAGS: /<[^>]*>/g,
  /** 清理多余空白字符 */
  EXTRA_WHITESPACE: /\s+/g,
  /** 匹配代码块 */
  CODE_BLOCK: /```[\s\S]*?```/g,
  /** 匹配链接 */
  LINKS: /https?:\/\/[^\s]+/g,
  /** 匹配邮箱 */
  EMAIL: /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g,
  /** 匹配时间戳 */
  TIMESTAMP: /\d{4}-\d{2}-\d{2}[\sT]\d{2}:\d{2}:\d{2}/g
} as const

/**
 * 通用选择器模式
 */
export const CommonSelectorPatterns = {
  /** 输入框通用模式 */
  INPUT_FIELD: [
    'textarea[placeholder*="message" i]',
    'textarea[placeholder*="prompt" i]',
    'div[contenteditable="true"]',
    'input[type="text"]',
    '[role="textbox"]'
  ],
  /** 发送按钮通用模式 */
  SEND_BUTTON: [
    'button[aria-label*="send" i]',
    'button[data-testid*="send" i]',
    'button[type="submit"]',
    'button:has(svg)',
    '[role="button"]:has(svg)'
  ],
  /** 消息容器通用模式 */
  MESSAGE_CONTAINER: [
    '[data-message-author-role]',
    '[data-testid*="message"]',
    '.message',
    '.chat-message',
    '[role="article"]'
  ]
} as const

/**
 * ChatGPT 平台配置
 */
export const ChatGPTConfig: PlatformConfig = {
  name: 'ChatGPT',
  id: 'chatgpt',
  url: 'https://chat.openai.com',
  patterns: {
    hostname: /^chat\.openai\.com$/,
    validPath: /^\/c\//
  }
}

/**
 * Claude 平台配置
 */
export const ClaudeConfig: PlatformConfig = {
  name: 'Claude',
  id: 'claude',
  url: 'https://claude.ai',
  patterns: {
    hostname: /^claude\.ai$/,
    validPath: /^\/chat|^\/c\//
  }
}

/**
 * DeepSeek 平台配置
 */
export const DeepSeekConfig: PlatformConfig = {
  name: 'DeepSeek',
  id: 'deepseek',
  url: 'https://chat.deepseek.com',
  patterns: {
    hostname: /^chat\.deepseek\.com$/
  }
}

/**
 * Gemini 平台配置
 */
export const GeminiConfig: PlatformConfig = {
  name: 'Gemini',
  id: 'gemini',
  url: 'https://gemini.google.com',
  patterns: {
    hostname: /^gemini\.google\.com$/
  }
}

/**
 * Kimi 平台配置
 */
export const KimiConfig: PlatformConfig = {
  name: 'Kimi',
  id: 'kimi',
  url: 'https://kimi.moonshot.cn',
  patterns: {
    // 支持多域名：kimi.moonshot.cn, kimi.com, www.kimi.com
    hostname: /^(.*\.)?(kimi\.moonshot\.cn|kimi\.com)$/
  }
}

/**
 * 平台配置集合
 */
export const PlatformConfigs = {
  chatgpt: ChatGPTConfig,
  claude: ClaudeConfig,
  deepseek: DeepSeekConfig,
  gemini: GeminiConfig,
  kimi: KimiConfig
} as const satisfies Record<string, PlatformConfig>

export const PlatformConfigList = Object.values(PlatformConfigs)
