---
type: "development_rules"
description: "EchoSync 项目目录结构和文件组织规则"
---

# 项目目录结构规则

## Chrome插件核心模块结构

### extension/src/ 主要目录
```
extension/src/
├── background/           # Background Script 模块
├── content/             # Content Script 模块
├── popup/               # Popup UI 模块
├── options/             # Options 页面模块
├── common/              # 共享业务逻辑
├── components/          # 可复用UI组件
├── stores/              # 状态管理
└── styles/              # 全局样式
```

## 各模块详细结构

### background/ - 后台服务模块
```
background/
├── index.ts                 # 主入口文件，最多20行
├── messageHandler.ts        # 消息处理中心
├── eventListeners.ts        # Chrome扩展事件监听
├── databaseConnection.ts    # 数据库连接管理
├── keepAlive.ts            # Service Worker保活
└── healthMonitor.ts        # 系统健康监控
```

### content/ - 内容脚本模块
```
content/
├── adapters/               # 平台适配器
├── capture/                # 页面元素捕捉
├── inject/                 # UI注入模块
├── components/             # Content专用UI组件
├── configs/                # 配置文件
├── model/                  # 数据模型
├── types/                  # 类型定义
├── utils/                  # 工具函数
├── ContentScriptManager.ts # 内容脚本管理器
└── index.ts               # 入口文件
```

### popup/ - 弹出窗口UI
```
popup/
├── components/             # 弹窗专用组件
├── pages/                  # 弹窗页面组件
├── App.tsx                # 弹窗应用主组件
├── main.tsx               # 入口文件
└── index.html             # HTML模板
```

### options/ - 选项页面UI
```
options/
├── components/             # 选项页专用组件
├── hooks/                  # 选项页专用hooks
├── pages/                  # 选项页面组件
├── stores/                 # 选项页状态管理
├── types/                  # 选项页类型定义
├── OptionsApp.tsx         # 选项页应用主组件
├── main.tsx               # 入口文件
└── index.html             # HTML模板
```

### common/ - 共享业务逻辑
```
common/
├── base/                   # 基础类和工具
├── dao/                    # 数据访问对象层
├── database/               # 数据库层配置
├── service/                # 业务服务层
├── types/                  # 类型定义
└── utils.ts               # 通用工具函数
```

## 文件大小限制

### 严格限制
- **单个文件**: 最大 300 行代码
- **主入口文件**: 最大 20 行（如 background/index.ts）
- **配置文件**: 最大 100 行

### 拆分策略
文件超过限制时必须拆分：
1. **按功能拆分** - 将不同功能分离到独立文件
2. **按层次拆分** - 将复杂逻辑分层处理
3. **按组件拆分** - 将大组件拆分为子组件

## 新文件创建位置规则

### 按模块类型创建
- **平台适配器**: `content/adapters/platformName.ts`
- **UI注入器**: `content/inject/ComponentNameInject.ts`
- **页面捕捉器**: `content/capture/CaptureName.ts`
- **发送消息**: `common/service/MessagingService.ts`
- **接收消息**: `background/messageHandler.ts`
- **其他模块访问数据库** `common/service/DatabaseProxy.ts`
- **业务服务**: `common/service/ServiceName.ts`
- **数据访问**: `common/dao/EntityNameDao.ts`
- **UI组件**: `components/ComponentName.tsx`

### 模块间依赖规则
- **UI层**: popup/options → common/service (通过消息)
- **内容脚本**: content → common/service (仅通过消息)
- **后台服务**: background → common/service → common/dao → common/database

### 禁止的依赖
- content 模块禁止直接导入 common/dao 或 common/database
- UI组件禁止直接导入业务逻辑模块
- 同级模块间禁止循环依赖

## 代码组织最佳实践

### 单一职责原则
- 每个文件只负责一个明确的功能
- 每个类只处理一个业务领域
- 每个方法只做一件事

### 错误处理
- 使用统一的错误处理模式
- 错误信息要清晰明确
- 使用 console.error 记录详细错误信息