/* HistoryBubble 模态页样式 */

/* 模态页容器 */
.echosync-history-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 2147483647;
  display: none;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
  font-size: 14px;
  line-height: 1.4;
}

/* 背景遮罩 */
.echosync-modal-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  opacity: 0;
  transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 降级方案：不支持backdrop-filter的浏览器 */
@supports not (backdrop-filter: blur(8px)) {
  .echosync-modal-backdrop {
    background: rgba(0, 0, 0, 0.6);
  }
}

/* 内容容器 */
.echosync-modal-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0.9);
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-radius: 16px;
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.15),
    0 8px 16px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(255, 255, 255, 0.2);
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  will-change: transform, opacity;
}

/* 显示状态 */
.echosync-history-modal.show .echosync-modal-backdrop {
  opacity: 1;
}

.echosync-history-modal.show .echosync-modal-content {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1);
}

/* 标题区域 */
.echosync-history-header {
  padding: 20px 24px 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  background: rgba(139, 92, 246, 0.03);
  position: sticky;
  top: 0;
  z-index: 1;
}

.echosync-history-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  text-align: center;
}

/* 历史记录列表容器 */
.echosync-history-list {
  max-height: 60vh;
  overflow-y: auto;
  padding: 8px 0;
}

/* 滚动条样式 */
.echosync-history-list::-webkit-scrollbar {
  width: 6px;
}

.echosync-history-list::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

.echosync-history-list::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.echosync-history-list::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* 历史记录项 */
.echosync-history-item {
  padding: 16px 24px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.echosync-history-item:last-child {
  border-bottom: none;
}

.echosync-history-item:hover {
  background: rgba(139, 92, 246, 0.05);
  transform: translateX(4px);
}

.echosync-history-item:active {
  background: rgba(139, 92, 246, 0.1);
  transform: translateX(2px) scale(0.98);
}

/* 历史记录项内容 */
.echosync-history-item-content {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.echosync-history-item-text {
  flex: 1;
  min-width: 0;
}

.echosync-history-item-prompt {
  color: #374151;
  font-weight: 500;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-word;
  line-height: 1.5;
}

.echosync-history-item-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
  font-size: 12px;
  color: #6b7280;
}

.echosync-history-item-time {
  white-space: nowrap;
}

.echosync-history-item-platforms {
  display: flex;
  align-items: center;
  gap: 6px;
}

/* 空状态 */
.echosync-history-empty {
  padding: 40px 24px;
  text-align: center;
  color: #6b7280;
  font-size: 14px;
}

/* Toast 提示 */
.echosync-toast {
  position: fixed;
  z-index: 2147483648;
  background: rgba(34, 197, 94, 0.95);
  color: white;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  transform: translateY(10px);
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  pointer-events: none;
  white-space: nowrap;
}

.echosync-toast.show {
  opacity: 1;
  transform: translateY(0);
}

.echosync-toast.error {
  background: rgba(239, 68, 68, 0.95);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .echosync-modal-content {
    width: 95%;
    max-width: none;
    max-height: 85vh;
    border-radius: 12px;
  }
  
  .echosync-history-header {
    padding: 16px 20px 12px;
  }
  
  .echosync-history-title {
    font-size: 16px;
  }
  
  .echosync-history-item {
    padding: 14px 20px;
  }
  
  .echosync-history-item-prompt {
    -webkit-line-clamp: 2;
  }
  
  .echosync-history-list {
    max-height: 65vh;
  }
}

@media (max-width: 480px) {
  .echosync-modal-content {
    width: 98%;
    max-height: 90vh;
    border-radius: 8px;
  }
  
  .echosync-history-header {
    padding: 12px 16px 8px;
  }
  
  .echosync-history-item {
    padding: 12px 16px;
  }
  
  .echosync-history-item-content {
    gap: 8px;
  }
}

/* 动画关键帧 */
@keyframes echosync-modal-show {
  from {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

@keyframes echosync-modal-hide {
  from {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
  to {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.9);
  }
}

@keyframes echosync-backdrop-show {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes echosync-backdrop-hide {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
