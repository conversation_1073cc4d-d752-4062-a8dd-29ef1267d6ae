---
type: "agent_requested"
description: "规则目录"
---
# EchoSync AI Extension - Augment Guidelines

## 技术栈

### Chrome 插件 (extension/)
- **框架**: React 18 + TypeScript 5
- **构建**: Vite 5 + @crxjs/vite-plugin 2
- **样式**: Tailwind CSS 3 + shadcn/ui
- **数据库**: indexedDB+ dexie
- **状态**: Zustand 4
- **路由**: React Router 6
- **测试**: Jest 29 + React Testing Library

## 核心开发规范

### 必须遵守的要求
1. **文件大小限制**: 单个ts文件不能超过300行，超过必须拆分
2. **简洁设计**: 遵循最简洁设计模式，避免过度设计
3. **继承限制**: 继承不超过2级，更多层次使用组合模式
4. **扩展优先**: 子类优先选择对父类的扩充，而不是完全覆盖
5. **数据库访问**: 所有数据库操作必须通过Message发送给background脚本
6. **测试策略**: 暂时不需要单元测试，专注功能实现

## 开发规则目录

### 📁 基础规则
- **[项目结构规则](./00-project-structure.md)**: 目录结构和文件组织规范
- **[命名规范](./01-naming-conventions.md)**: 文件、变量、函数命名规则
- **[UI组件规范](./02-ui-components.md)**: React和Content Script UI组件开发规范

### 🔧 Content Script模块
- **[模块总览](./content/content-overview.md)**: Content模块架构和设计原则
- **[中介者模式](./content/mediator.md)**: BaseAIAdapter实现和选择器管理
- **[页面捕捉](./content/capture.md)**: 页面元素捕捉组件开发规则
- **[UI注入](./content/inject.md)**: UI注入组件开发规则
- **[数据模型](./content/model.md)**: Model单例和数据共享规范
- **[依赖注入](./content/dependency-injection.md)**: DI框架使用和最佳实践

### 🎯 Popup模块
- **[Popup开发规则](./popup/popup.md)**: 弹出窗口UI开发规范

### ⚙️ Options模块
- **[Options开发规则](./options/options.md)**: 选项页面开发规范

### 🔙 Background模块
- **[Background总览](./background/background.md)**: 后台服务模块开发规则
- **[数据库操作](./background/database.md)**: 数据库操作流程和规范

## 规则使用指南

### 按模块查找规则
- **Content Script开发** → 查看 content/ 目录下的规则
- **Popup开发** → 查看 popup/popup.md
- **Options开发** → 查看 options/options.md
- **Background开发** → 查看 background/ 目录下的规则
- **UI组件开发** → 查看 02-ui-components.md
- **项目结构** → 查看 00-project-structure.md
- **命名规范** → 查看 01-naming-conventions.md

### 开发流程建议
1. **新功能开发前**: 先查看对应模块的规则文件
2. **组件开发**: 根据组件类型查看UI组件规范
3. **数据操作**: 严格遵循background/database.md规则
4. **平台适配**: 遵循content/mediator.md中介者模式
5. **代码审查**: 对照检查清单验证是否符合规范

