# 需求-获得提示词答案

获得提示词答案，捕捉并存储回答。以当前浏览器的的`https://www.kimi.com/chat/d2mr8ius1rh76j73lqjg`为例，完成对当前页面发送问题捕捉，等待返回捕捉，使用答案自带的复制功能获得答案，完整流程。
要求：
1. 在content/capture目录新建`AskAnswerCapture.ts`，该脚本继承自Singleton，在InputCapture中引入实例。
2. 在`AskAnswerCapture`中引入`ArchiveServivce`实例
3. `AskAnswerCapture.ts`中捕捉到页面点击了send按钮后，发送事件，ArchiveButtonInject.ts收到事件后，执行`this.component.showArchivedState()显示
4. 同时调用`ArchiveServivce中的`archivePrompt`方法存档提示词
5. `AskAnswerCapture`要能检测到当前发送成功后的答案
6. 获得答案，调用`ArchiveServivce中的`archiveAnswer`方法存档答案
7. 在archiveAnswer中存入chat_history表，存储过程还是先调用`ChatHistoryDatabaseProxy`中的方法，由该方法发送MessagingService.sendToBackground，然后在background调用`ChatHistoryService.ts`中的方法
8. 任务规划时，要参考项目规则文档