---
type: "development_rules"
description: "Chrome插件Options模块React开发规则"
---

# Options模块开发规则

## 模块职责
- 提供插件配置和设置管理界面
- 管理用户个性化设置和偏好
- 处理配置数据的存储和同步
- 管理插件权限相关设置

## 技术架构
- **框架**: React 18 + TypeScript 5
- **样式**: Tailwind CSS 3 + shadcn/ui
- **数据持久化**: Chrome Storage API（通过Background）
- **状态管理**: React Hooks + Context

## 项目结构
```
options/
├── components/           # 配置组件
│   ├── SettingSection.tsx
│   ├── ToggleSwitch.tsx
│   └── SelectDropdown.tsx
├── pages/               # 页面组件
│   ├── GeneralSettings.tsx
│   ├── PlatformSettings.tsx
│   └── AdvancedSettings.tsx
├── hooks/               # 自定义Hooks
│   ├── useSettings.ts
│   └── useStorage.ts
├── App.tsx             # 主应用组件
├── main.tsx            # 入口文件
└── index.html          # HTML模板
```

## 自定义Hooks规范
### 核心Hooks
- **useSettings**: 管理插件设置的读取和更新
- **useStorage**: 封装Chrome Storage API操作
- **usePlatformConfig**: 管理平台特定配置
- **usePermissions**: 处理权限状态和请求

### Hook示例
```typescript
const useSettings = () => {
  const [settings, setSettings] = useState<SettingsType | null>(null);
  const [loading, setLoading] = useState(true);

  const updateSetting = useCallback(async (key: string, value: any) => {
    const result = await MessagingService.sendToBackground({
      type: MessageType.UPDATE_SETTING,
      payload: { key, value }
    });

    if (result.success) {
      setSettings(prev => ({ ...prev, [key]: value }));
    }
  }, []);

  return { settings, loading, updateSetting };
};
```

## 数据管理规范

### 配置数据结构
```typescript
interface SettingsType {
  general: {
    enabled: boolean;
    autoSave: boolean;
  };
  platforms: {
    [key: string]: PlatformConfig;
  };
  ui: {
    theme: 'light' | 'dark';
    language: string;
  };
}
```

### 与Background通信
- 严格遵循 background/database.md 规则
- 使用 MessagingService.sendToBackground 发送请求
- 处理 DatabaseResult 格式的返回数据
- 实现错误处理和重试机制

## 组件开发规范

### 表单组件
- 继承通用UI组件规范（参考 02-ui-components.md）
- 使用受控组件模式
- 实现表单验证和错误提示
- 支持键盘导航和无障碍访问

### 布局组件
- 使用响应式设计适配不同屏幕
- 实现侧边栏导航和内容区域
- 支持设置分组和折叠展开

## 用户体验规范

### 界面设计
- 提供清晰的设置分类和导航
- 实现设置搜索和快速定位
- 显示设置保存状态和进度
- 提供操作成功和失败的反馈

### 性能优化
- 使用懒加载减少初始加载时间
- 实现设置的防抖保存
- 缓存常用配置减少重复请求

## 开发检查清单

### 页面开发检查
- [ ] 使用 React + TypeScript 开发
- [ ] 实现响应式布局设计
- [ ] 遵循 background/database.md 规则进行数据操作
- [ ] 支持键盘导航和无障碍访问
- [ ] 添加适当的错误处理
- [ ] 文件大小不超过 300 行

### Hooks开发检查
- [ ] 使用 `use` 前缀命名
- [ ] 实现清晰的功能封装
- [ ] 处理异步操作和错误
- [ ] 添加 TypeScript 类型定义
- [ ] 实现资源清理逻辑

### 数据管理检查
- [ ] 使用 MessagingService 与Background通信
- [ ] 处理 DatabaseResult 格式返回数据
- [ ] 实现配置验证和约束检查
- [ ] 添加配置变更的实时同步