/**
 * 通用单例基类
 * 每个继承此类的子类都拥有独立的单例实例
 */
export abstract class Singleton<T> {
  private static instance: any;

  /**
   * 获取单例实例
   * @returns 单例实例
   */
  public static getInstance<T extends Singleton<any>>(this: new () => T): T {
    if (!(this as any).instance) {
      console.log(`【Singleton】Creating instance for: ${this.name}`);
      (this as any).instance = new this();
    }
    
    return (this as any).instance;
  }


  /**
   * 检查当前类是否已有单例实例
   */
  public static hasInstance<T extends Singleton<any>>(this: new () => T): boolean {
    return !!(this as any).instance;
  }

  /**
   * 清理业务资源的方法，子类可以重写此方法来清理特定资源
   * 注意：此方法不会重置单例实例，只负责清理业务资源
   */
  public destroy?(): void{
    (this as any).instance = null;
  }
}
