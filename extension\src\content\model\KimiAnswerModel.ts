import { Singleton } from "../../common/base/Singleton";
import { ConversationData, QuestionAnswerPair } from "../types/KimiTypes";

/**
 * Kimi答案数据模型
 * 继承Singleton，作为单例类存储聊天数据
 * 为KimiAnswerController和KimiAnswerService提供数据访问接口
 */
export class KimiAnswerModel extends Singleton<KimiAnswerModel> {
    // private conversationMap: Map<string, ConversationData> = new Map();
    private currentTitle: string = '';
    private currentChatId: string = ''; // 当前聊天组的id
    private readonly MAX_CONVERSATIONS = 10; // 最大对话数量

    private promptList: string[] = [];
    private answerList: string[] = [];

    /**
     * 更新对话标题
     */
    public setCurrentTitle(title: string): void {
        this.currentTitle = title;
    }
    
    /**
     * 获取当前标题
     */
    public getCurrentTitle(): string {
        return this.currentTitle;
    }

    public setChatId(chatId: string): void {
        this.currentChatId = chatId;
    }

    public addPrompt(prompt: string): void {
        const displayPrompt = prompt.length > 10 ? prompt.slice(0, 10) + '...' : prompt;
        console.log('[KimiAnswerModel] 添加问题 index：%d, %s', this.promptList.length, displayPrompt);
        this.promptList.push(prompt);
    }

    public addAnswer(answer: string): void {
        const displayAnswer = answer.length > 10 ? answer.slice(0, 10) + '...' : answer;
        console.log('[KimiAnswerModel] Answer index：%d, %s', this.answerList.length, displayAnswer);
        this.answerList.push(answer);
    }

    // /**
    //  * 确保对话存在（公开方法）
    //  * @param title 对话标题
    //  */
    // public ensureConversationExists(title: string): void {
    //     this._ensureConversationExists(title);
    // }

    // /**
    //  * 确保对话存在
    //  * @param title 对话标题
    //  */
    // private _ensureConversationExists(title: string): void {
    //     if (!this.conversationMap.has(title)) {
    //         const conversationData: ConversationData = {
    //             title,
    //             qaList: [],
    //             createdAt: Date.now(),
    //             updatedAt: Date.now()
    //         };
    //         this.conversationMap.set(title, conversationData);
    //         console.info(`[KimiAnswerModel] 创建新对话: ${title}`);
    //     }
    // }

    // /**
    //  * 保存问答对
    //  * @param question 问题内容
    //  * @param answer 答案内容
    //  */
    // public async saveQuestionAnswerPair(question: string, answer: string): Promise<void> {
    //     try {
            
    //         const conversation = this.conversationMap.get(this.currentTitle);
    //         if (!conversation) {
    //             console.error('[KimiAnswerModel] 保存问答对失败：未找到当前对话', { title: this.currentTitle });
    //             return;
    //         }
            
    //         const qaPair: QuestionAnswerPair = {
    //             question,
    //             answer,
    //             timestamp: Date.now(),
    //             questionId: this.generateId(),
    //             answerId: this.generateId()
    //         };
            
    //         conversation.qaList.push(qaPair);
    //         conversation.updatedAt = Date.now();
            
    //         console.info('[KimiAnswerModel] 问答对保存成功', { 
    //             title: this.currentTitle, 
    //             questionLength: question.length,
    //             answerLength: answer.length,
    //             totalPairs: conversation.qaList.length
    //         });
            
    //         // 发送到后台存储
    //         await this.sendToBackground(conversation);
            
    //     } catch (error) {
    //         console.error('[KimiAnswerModel] 保存问答对失败', error);
    //     }
    // }

    /**
     * 发送数据到后台
     * @param conversationData 对话数据
     */
    private async sendToBackground(conversationData: ConversationData): Promise<void> {
        try {
            // 这里将数据发送到后台进行存储
            // 在实际项目中，需要使用 MessagingService 或类似的服务
            console.info('[KimiAnswerModel] 发送对话数据到后台', {
                title: conversationData.title,
                qaCount: conversationData.qaList.length
            });
            
            // TODO: 实现实际的后台数据发送
            // await MessagingService.sendToBackground({
            //     type: 'SAVE_CONVERSATION',
            //     data: conversationData
            // });
            
        } catch (error) {
            console.error('[KimiAnswerModel] 发送数据到后台失败', error);
        }
    }


    
    /**
     * 清理所有对话数据
     */
    public clearAllConversations(): void {
        // const conversationCount = this.conversationMap.size;
        // this.conversationMap.clear();
        this.currentTitle = '';
        this.currentChatId = '';
        
        // 清理数组数据，而不是方法引用
        this.promptList.length = 0;  // 清空数组
        this.answerList.length = 0;  // 清空数组
        
        console.info('[KimiAnswerModel] 清理了所有对话数据');
    }


    /**
     * 生成唯一ID
     */
    private generateId(): string {
        return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * 清理业务资源
     */
    public destroy(): void {
        this.clearAllConversations();
        console.info('[KimiAnswerModel] destroyed');
        super.destroy();
    }

    /**
     * 完全重置单例实例（静态方法）
     * 注意：这会清理所有数据并重置单例实例
     */
    public static resetSingleton(): void {
        KimiAnswerModel.resetInstance();
    }
}