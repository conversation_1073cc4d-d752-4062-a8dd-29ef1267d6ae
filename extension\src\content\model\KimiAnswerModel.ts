import { Singleton } from "../../common/base/Singleton";
import { ConversationData, QuestionAnswerPair } from "../types/KimiTypes";

/**
 * Kimi答案数据模型
 * 继承Singleton，作为单例类存储聊天数据
 * 为KimiAnswerController和KimiAnswerService提供数据访问接口
 */
export class KimiAnswerModel extends Singleton<KimiAnswerModel> {
    private conversationMap: Map<string, ConversationData> = new Map();
    private currentTitle: string = '';
    private currentChatId: string = '';
    private readonly MAX_CONVERSATIONS = 10; // 最大对话数量

    /**
     * 更新对话标题
     */
    public setCurrentTitle(title: string): void {
        this.currentTitle = title;
    }

    public setChatId(chatId: string): void {
        this.currentChatId = chatId;
    }

    /**
     * 确保对话存在（公开方法）
     * @param title 对话标题
     */
    public ensureConversationExists(title: string): void {
        this._ensureConversationExists(title);
    }

    /**
     * 确保对话存在
     * @param title 对话标题
     */
    private _ensureConversationExists(title: string): void {
        if (!this.conversationMap.has(title)) {
            const conversationData: ConversationData = {
                title,
                qaList: [],
                createdAt: Date.now(),
                updatedAt: Date.now()
            };
            this.conversationMap.set(title, conversationData);
            console.info(`[KimiAnswerModel] 创建新对话: ${title}`);
        }
    }

    /**
     * 保存问答对
     * @param question 问题内容
     * @param answer 答案内容
     */
    public async saveQuestionAnswerPair(question: string, answer: string): Promise<void> {
        try {
            
            const conversation = this.conversationMap.get(this.currentTitle);
            if (!conversation) {
                console.error('[KimiAnswerModel] 保存问答对失败：未找到当前对话', { title: this.currentTitle });
                return;
            }
            
            const qaPair: QuestionAnswerPair = {
                question,
                answer,
                timestamp: Date.now(),
                questionId: this.generateId(),
                answerId: this.generateId()
            };
            
            conversation.qaList.push(qaPair);
            conversation.updatedAt = Date.now();
            
            console.info('[KimiAnswerModel] 问答对保存成功', { 
                title: this.currentTitle, 
                questionLength: question.length,
                answerLength: answer.length,
                totalPairs: conversation.qaList.length
            });
            
            // 发送到后台存储
            await this.sendToBackground(conversation);
            
        } catch (error) {
            console.error('[KimiAnswerModel] 保存问答对失败', error);
        }
    }

    /**
     * 发送数据到后台
     * @param conversationData 对话数据
     */
    private async sendToBackground(conversationData: ConversationData): Promise<void> {
        try {
            // 这里将数据发送到后台进行存储
            // 在实际项目中，需要使用 MessagingService 或类似的服务
            console.info('[KimiAnswerModel] 发送对话数据到后台', {
                title: conversationData.title,
                qaCount: conversationData.qaList.length
            });
            
            // TODO: 实现实际的后台数据发送
            // await MessagingService.sendToBackground({
            //     type: 'SAVE_CONVERSATION',
            //     data: conversationData
            // });
            
        } catch (error) {
            console.error('[KimiAnswerModel] 发送数据到后台失败', error);
        }
    }

    /**
     * 获取所有对话数据
     */
    public getAllConversations(): ConversationData[] {
        return Array.from(this.conversationMap.values());
    }

    /**
     * 获取指定对话数据
     * @param title 对话标题
     */
    public getConversation(title: string): ConversationData | undefined {
        return this.conversationMap.get(title);
    }

    /**
     * 获取对话统计信息
     */
    public getConversationStats(): {
        totalConversations: number;
        totalQuestionPairs: number;
        currentTitle: string;
    } {
        const totalQuestionPairs = Array.from(this.conversationMap.values())
            .reduce((sum, conv) => sum + conv.qaList.length, 0);
            
        return {
            totalConversations: this.conversationMap.size,
            totalQuestionPairs,
            currentTitle: this.currentTitle
        };
    }

    /**
     * 清理所有对话数据
     */
    public clearAllConversations(): void {
        const conversationCount = this.conversationMap.size;
        this.conversationMap.clear();
        this.currentTitle = '';
        console.info(`[KimiAnswerModel] 清理了 ${conversationCount} 个对话数据`);
    }

    /**
     * 获取当前标题
     */
    public getCurrentTitle(): string {
        return this.currentTitle;
    }

    /**
     * 生成唯一ID
     */
    private generateId(): string {
        return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * 清理业务资源
     */
    public destroy(): void {
        this.clearAllConversations();
        console.info('[KimiAnswerModel] 业务资源已清理');
        super.destroy();
    }
}