/**
 * 选择器管理服务，提供多选择器策略提升兼容性
 */
export class KimiSelectorService {
    // 预定义多个选择器确保兼容性
    private static readonly COMPLETION_SELECTORS = [
        '.segment-assistant-actions-content .simple-button.size-small',
        '.assistant-actions .copy-button',
        '.response-actions .action-button',
        '[data-testid="copy-button"]',
        '.simple-button.size-small',
        '.copy-button',
        '.action-button',
        '[aria-label*="复制"]',
        '[aria-label*="Copy"]',
        '.segment-assistant-actions .simple-button',
        '.assistant-bottom-bar .copy-btn',
        '.kimi-copy-button',
        '.response-copy-button',
        '[data-action="copy"]',
        '.btn-copy',
        'button[title*="复制"]',
        'button[title*="Copy"]'
    ];
    
    /**
     * 查找完成按钮
     * @param container 容器元素
     * @returns 找到的按钮元素或null
     */
    public static findCompletionButton(container: Element): Element | null {
        for (const selector of this.COMPLETION_SELECTORS) {
            const element = container.querySelector(selector);
            if (element) {
                console.info(`[KimiSelectorService] 找到完成按钮：${selector}`);
                return element;
            }
        }
        console.warn(`[KimiSelectorService] 未找到任何完成按钮`);
        return null;
    }
    
    /**
     * 获取所有完成选择器
     */
    public static getCompletionSelectors(): string[] {
        return [...this.COMPLETION_SELECTORS];
    }
    
    /**
     * 检查元素是否为完成按钮
     */
    public static isCompletionButton(element: Element): boolean {
        return this.COMPLETION_SELECTORS.some(selector => {
            try {
                return element.matches(selector);
            } catch (error) {
                return false;
            }
        });
    }
}