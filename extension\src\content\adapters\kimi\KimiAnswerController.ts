import { Singleton } from "@/common/base";
import { BaseAIAdapter } from "../BaseAIAdapter";
import { ErrorType, ErrorHandler } from "../../types/ErrorTypes";
import { ConversationData} from "../../types/KimiTypes";
import { KimiSelectorService } from "./KimiSelectorService";
import { KimiPageService } from "./KimiPageService";
import { KimiClipboardService } from "./KimiClipboardService";
import { KimiAnswerService } from "./KimiAnswerService";
import { logDebug, logError, logInfo } from "@/content/utils/logUtils";
import { DOMUtils } from "@/content/utils/DOMUtils";
import { KimiAnswerModel } from "@/content/model/KimiAnswerModel";
import SelectorManager from "../../configs/SelectorManager";


/**
 * Kimi平台答案捕获控制器
 * 负责流程编排和协调各Service层的工作
 */
export class KimiAnswerController {
    // 适配器引用
    private adapter: BaseAIAdapter | null = null;

    // 服务层组件
    private answerService: KimiAnswerService = new KimiAnswerService();

    // 状态管理
    private currentPageType: 'home' | 'chat' | 'unknown' = 'unknown';

    // 配置参数

    /**
     * 初始化监听
     * @param adapter BaseAIAdapter实例
     */
    public init(adapter: BaseAIAdapter): void {
        this.adapter = adapter;
        logInfo('KimiAnswerController 初始化开始');
        // 立即检查当前页面类型并启动相应服务
        this.checkPageTypeAndInitServices();

        logInfo('KimiAnswerController 初始化完成');
    }

    /**
     * 检查页面类型并初始化服务
     */
    private checkPageTypeAndInitServices(): void {
        try {
            // 使用URL判断页面类型
            const urlResult = KimiPageService.getPageTypeFromUrl();

            let newPageType: 'home' | 'chat' | 'unknown';
            if (urlResult.isHome) {
                newPageType = 'home';
            } else if (urlResult.isChat) {
                newPageType = 'chat';
            } else {
                newPageType = 'unknown';
            }

            logInfo('页面类型检测', {
                url: window.location.href,
                pageType: newPageType,
                chatId: urlResult.chatId
            });

            this.currentPageType = newPageType;
            switch (newPageType) {
                case 'home':
                    logInfo('进入Home页，停止所有监听器');
                    this.stopAllListeners();
                    break;

                case 'chat':
                    KimiAnswerModel.getInstance().setChatId(urlResult.chatId);
                    logInfo(`进入Chat页，启动监听器 (chatId: ${urlResult.chatId})`);
                    this.startChatListeners();
                    break;

                case 'unknown':
                    logInfo('未知页面类型，停止所有监听器');
                    this.stopAllListeners();
                    break;
            }

        } catch (error) {
            logError('页面类型检测失败', error);
        }
    }


    /**
     * 启动聊天页监听器
     * 异步启动监听器，即关心的节点出现后，才启动监听器
     */
    private async startChatListeners(): Promise<void> {
        try {
            // 1. 等待并处理聊天标题
            await this.handleChatHeader();
            
            // 2. 等待聊天列表节点并启动监听
            await this.handleChatList();
            
        } catch (error) {
            logError('启动聊天页监听器失败', error);
        }
    }

    /**
     * 处理聊天标题
     */
    private async handleChatHeader(): Promise<void> {
            const selectors = SelectorManager.getSelector();
        const el_header = await DOMUtils.asyncSelectElement(selectors.headerContent);
        logInfo('已找到header节点', el_header);
        
        if (el_header == null) {
            logError('未找到header节点');
            return;
        }
        
        // 提取并更新对话标题
        const h2Element = DOMUtils.findElementInContainer(el_header, selectors.headerValue);
        if (h2Element) { 
            const title = h2Element.textContent?.trim() || '';
            logInfo('对话标题:', title);
            
            // 更新数据模型中的标题
            const answerModel = KimiAnswerModel.getInstance();
            answerModel.setCurrentTitle(title);
            answerModel.ensureConversationExists(title);
        } else {
            logError('未找到h2标题元素');
        }
    }

    /**
     * 处理聊天列表并启动监听
     */
    private async handleChatList(): Promise<void> {
        const selectors = SelectorManager.getSelector();
        if (!selectors?.chatContentList) {
            logError('未找到聊天内容列表选择器配置');
            return;
        }

        const el_chat_list = await DOMUtils.asyncSelectElement(selectors.chatContentList);
        logInfo('已找到聊天列表节点', el_chat_list);

        if (el_chat_list == null) {
            logError('未找到聊天列表节点');
            return;
        }

        // 将聊天列表节点传递给 KimiAnswerService 并启动监听
        await this.answerService.startListeningOnChatList(el_chat_list);
        logInfo('KimiAnswerController 流程控制完成，业务监听已交给 KimiAnswerService');
    }

    /**
     * 停止所有监听器
     */
    private stopAllListeners(): void {
        this.answerService.stopAnswerListener();
    }


    /**
     * 销毁服务，清理所有资源
     */
    public destroy(): void {
        try {
            logInfo('开始销毁 KimiAnswerController');

            // 清理答案服务
            this.answerService.destroy();

            // 重置状态
            this.currentPageType = 'unknown';

            // 清理适配器引用
            this.adapter = null;

            logInfo('KimiAnswerController 销毁完成');

        } catch (error) {
            console.error('[KimiAnswerController] 销毁过程中发生错误:', error);
        }
    }

    /**
     * 手动刷新页面状态（当URL变化时调用）
     */
    public refreshPageState(): void {
        logInfo('手动刷新页面状态');
        this.checkPageTypeAndInitServices();
    }

}