/**
 * DOM操作工具类
 */
export class DOMUtils {

  /**
   * 等待元素出现
   */
  static asyncSelectElement(selectors: string[], timeout = 5000): Promise<Element | null> {
    return new Promise((resolve, reject) => {
      // 先尝试直接查找所有选择器
      for (const selector of selectors) {
        const element = document.querySelector(selector)
        if (element) {
          return resolve(element);
        }
      }

      const observer = new MutationObserver((mutations, obs) => {
        for (const mutation of mutations) {
          // 1 判断target 是否为元素节点
          if (mutation.type !== "childList") {
            continue;
          }

          // 检查target是否匹配任一选择器
          const targetElement = mutation.target as HTMLElement;
          for (const selector of selectors) {
            if (targetElement.classList.contains(selector)) {
              obs.disconnect();
              return resolve(targetElement);
            }
          }

          for (const node of mutation.addedNodes) {
            if (node.nodeType !== Node.ELEMENT_NODE) { // 只要元素节点
              continue;
            }

            // node转element
            const element = node as HTMLElement
            // 检查新增节点是否匹配任一选择器
            for (const selector of selectors) {
              if (element.classList.contains(selector)) {
                obs.disconnect();
                return resolve(element);
              }
              
              const target = element.querySelector(selector);
              if (target) {
                obs.disconnect();
                return resolve(target);
              }
            }
          }
        }

        // 每次mutation后都重新检查一遍所有选择器
        for (const selector of selectors) {
          const el = document.querySelector(selector)
          if (el) {
            obs.disconnect()
            return resolve(el)
          }
        }
      })

      observer.observe(document.body, { childList: true, subtree: true })

      // 超时处理
      setTimeout(() => {
        observer.disconnect()
        resolve(null)
      }, timeout)
    })
  }

  /**
   * 等待页面加载完成
   */
  static waitForPageLoad(): Promise<void> {
    return new Promise((resolve) => {
      if (document.readyState === 'complete') {
        resolve()
        return
      }

      const handleLoad = () => {
        document.removeEventListener('DOMContentLoaded', handleLoad)
        window.removeEventListener('load', handleLoad)
        resolve()
      }

      document.addEventListener('DOMContentLoaded', handleLoad)
      window.addEventListener('load', handleLoad)
    })
  }



  /**
   * 检查元素是否可见
   */
  static isVisibleElement(element: HTMLElement): boolean {
    const rect = element.getBoundingClientRect()
    const style = window.getComputedStyle(element)

    return rect.width > 0 &&
      rect.height > 0 &&
      style.display !== 'none' &&
      style.visibility !== 'hidden' &&
      style.opacity !== '0'
  }

  /**
   * 检查元素是否准备就绪（存在且可见）
   */
  static isElementReady(element: HTMLElement): boolean {
    const rect = element.getBoundingClientRect();
    const style = window.getComputedStyle(element);

    return rect.width > 0 &&
      rect.height > 0 &&
      style.display !== 'none' &&
      style.visibility !== 'hidden' &&
      style.opacity !== '0';
  }

  /**
   * 等待关键DOM元素可用
   * @param selectors 选择器数组
   * @param maxAttempts 最大尝试次数，默认20次
   * @param delay 每次尝试间隔，默认250ms
   */
  static async waitForKeyElements(selectors: string[], maxAttempts = 20, delay = 250): Promise<void> {
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      console.log(`【EchoSync】Waiting for key elements, attempt ${attempt}/${maxAttempts}`);

      // 检查输入框是否存在且可见
      for (const selector of selectors) {
        const element = document.querySelector(selector) as HTMLElement;
        if (element && this.isElementReady(element)) {
          console.log('【EchoSync】Key elements found, selector:', selector);
          return; // 找到可用元素，退出等待
        }
      }

      // 如果是最后一次尝试，记录警告但继续初始化
      if (attempt === maxAttempts) {
        console.warn('【EchoSync】Key elements not found after maximum attempts, proceeding anyway');
        return;
      }

      // 等待一段时间后重试
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }


  /**
   * 查找元素（在document中查找）
   * @param selectors 元素选择器数组
   * @returns 找到的第一个容器元素或null
   */
  static findElement(selectors: string[]): HTMLElement | null {
    for (const selector of selectors) {
      const element = document.querySelector(selector) as HTMLElement
      if (element) {
        return element
      }
    }
    return null
  }

  /**
   * 查找所有匹配的元素（在document中查找）
   * @param selectors 元素选择器数组
   * @returns 所有匹配的元素数组
   */
  static findElementAll(selectors: string[]): HTMLElement[] {
    const elements: HTMLElement[] = [];
    for (const selector of selectors) {
      const foundElements = document.querySelectorAll(selector);
      foundElements.forEach(element => {
        elements.push(element as HTMLElement);
      });
    }
    return elements;
  }

  /**
   * 在指定节点内查找元素
   * @param container 容器节点
   * @param selectors 元素选择器数组
   * @returns 找到的第一个元素或null
   */
  static findElementInContainer(container: Element | Document, selectors: string[]): Element | null {
    for (const selector of selectors) {
      try {
        const element = container.querySelector(selector);
        if (element) {
          console.debug(`[DOMUtils] 找到元素，选择器: ${selector}`);
          return element;
        }
      } catch (error) {
        console.warn(`[DOMUtils] 选择器无效: ${selector}`, error);
      }
    }
    console.debug(`[DOMUtils] 未找到任何元素，尝试的选择器:`, selectors);
    return null;
  }


  /**
   * 在指定节点内查找所有匹配的元素
   * @param container 容器节点
   * @param selectors 元素选择器数组
   * @returns 所有匹配的元素数组
   */
  static findElementAllInContainer(container: Element | Document, selectors: string[]): Element[] {
    const elements: Element[] = [];
    for (const selector of selectors) {
      try {
        const foundElements = container.querySelectorAll(selector);
        if (foundElements.length > 0) {
          console.debug(`[DOMUtils] 找到${foundElements.length}个元素，选择器: ${selector}`);
          foundElements.forEach(element => elements.push(element));
        }
      } catch (error) {
        console.warn(`[DOMUtils] 选择器无效: ${selector}`, error);
      }
    }
    console.debug(`[DOMUtils] 共找到${elements.length}个元素，尝试的选择器:`, selectors);
    return elements;
  }

}
