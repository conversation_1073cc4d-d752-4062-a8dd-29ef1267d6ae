import { BaseCapture } from './BaseCapture';
import { BaseAIAdapter } from '../adapters/BaseAIAdapter';
import { DOMUtils } from '../utils/DOMUtils';
import { EchoSyncEventEnum } from '../types/DOMEnum';
import { InputModel } from '../model/InputModel';
import { AskCapture } from './AskCapture';
import SelectorManager from '../configs/SelectorManager';

/**
 * 输入框捕捉类
 * 使用简单的构造函数依赖注入
 */
export class InputCapture extends BaseCapture {

  // 输入元素
  private inputElement: HTMLElement | null = null;
  private lastInputValue: string = '';
  private adapter: BaseAIAdapter | null = null;
  private askCapture: AskCapture;

  constructor(
    adapter: BaseAIAdapter,
    private inputModel: InputModel
  ) {
    super();
    this.adapter = adapter;
    this.initCaptureElement();
    this.initEventListener();
    this.askCapture = new AskCapture(adapter, inputModel);
  }


  /**
   * 获得input元素
   */
  protected initCaptureElement() {
    this.inputElement = DOMUtils.findElement(SelectorManager.getSelector()?.inputField)
    if (!this.inputElement) {
        console.error('【EchoSync】Input element captured error，selectors:', SelectorManager.getSelector()?.inputField)
    }
  }

  /**
   * 初始化对ui事件的监听
   */
  protected initEventListener(): void {
    this.setupFocusListener()

    // 监听键盘事件（Enter键）
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Enter' && !e.shiftKey && this.inputElement &&
        document.activeElement === this.inputElement) {

        // 检查是否可以发送
        if (this.askCapture.isButtonEnable()) {
          console.log('【EchoSync】Send triggered by Enter key');
          this.askCapture.handleSendEvent();
        }
      }
    });
  }

  /**
   * 设置输入框聚焦监听
   */
  private setupFocusListener(): void {
    const inputField = this.inputElement
    if (!inputField) {
      console.warn('【EchoSync】Input field not found, cannot set up focus listener')
      return
    }

    inputField.addEventListener('focusin', (e) => {
      const target = e.target as HTMLElement
      // 设置输入监听
      this.setupInputMonitoring(inputField)
      // 触发输入框聚焦事件
      document.dispatchEvent(new CustomEvent(EchoSyncEventEnum.INPUT_FOCUSED, {
        detail: { element: target }
      }))
    })
  }

  /**
   * 设置输入监听
   */
  private setupInputMonitoring(inputElement: HTMLElement): void {
    if (!inputElement) return

    // 监听输入变化
    const handleInput = () => {
      const currentValue = this.getCurrentInput()
      // console.info('【EchoSync】Input changed, value:', currentValue)
      if (currentValue !== this.lastInputValue) {
        this.lastInputValue = currentValue
        this.inputModel.setCurrPrompt(currentValue)

        // 触发输入变化事件
        document.dispatchEvent(new CustomEvent(EchoSyncEventEnum.INPUT_CHANGED, {
          detail: { value: currentValue }
        }))
      }
    }

    this.inputElement.addEventListener('input', handleInput)
    this.inputElement.addEventListener('change', handleInput)
    this.inputElement.addEventListener('keyup', handleInput)
  }

  /**
   * 获取当前输入内容
   */
  public getCurrentInput(): string {
    if (!this.inputElement) return ''
    
    // 获取输入框的值
    const value = (this.inputElement as HTMLInputElement).value || 
                 (this.inputElement as HTMLTextAreaElement).value || 
                 this.inputElement.textContent || ''
                 
    return value.trim()
  }


  /**
   * 获取最后输入值
   */
  public getLastInputValue(): string {
    return this.lastInputValue
  }

  public getInputElement(): HTMLElement | null {
    if (!this.inputElement) {
     this.inputElement = DOMUtils.findElement(SelectorManager.getSelector()?.inputField)
    }
    return this.inputElement
  }

  /**
   * 获取当前的promptUid
   * @returns 
   */
  public getCurrPromptUid(): string {
    return this.inputModel.getCurrPromptUid()
  }

  /**
   *  获取当前的chatUid（保持向后兼容）
   * @returns 
   * @deprecated 使用getCurrPromptUid代替
   */
  public getCurrChatUid(): string {
    return this.inputModel.getCurrChatUid()
  }

  /**
   * 销毁
   */
  destroy(): void {
    // 清理AskAnswerCapture
    if (this.askCapture) {
      this.askCapture.destroy();
    }

    this.inputElement = null
    this.lastInputValue = ''
  }
}