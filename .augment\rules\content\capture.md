---
type: "agent_requested"
description: "页面元素捕捉组件开发规则"
---
# 页面元素捕捉规则

## 核心设计原则

### 职责分离
- **Capture 模块**: 只观察页面元素，不修改DOM
- **Inject 模块**: 只修改DOM，不观察页面变化
- **Service 模块**: 处理共享的业务逻辑，不直接操作DOM
- **Adapter**: 协调各模块，处理平台特定逻辑

## Capture 组件开发规范

### 基础结构要求
- **位置**: `extension/src/content/capture/`
- **命名**: `ComponentNameCapture.ts`
- **继承**: 继承 BaseCapture 或遵循相同模式
- **依赖**: 通过构造函数接收 BaseAIAdapter 实例

### 选择器使用规则
- 必须使用 DOMUtils.findElement 方法查找元素
- 使用 adapter.mergedSelectors 获取合并后的选择器
- 选择器优先级：平台特定 > 通用选择器

### 事件监听规范
- 需要同时通知2个组件以上，才使用事件系统
- 使用原生 addEventListener 进行事件监听
- 事件命名使用 'echosync:' 前缀，定义在`DOMEnum.ts`中的EchoSyncEventEnum枚举

## DOMUtils 工具规范
### 元素查找方法
- **findElement()**: 按优先级查找元素
- **isElementVisible()**: 检查元素可见性
- **waitForElement()**: 等待元素出现

## 自定义事件规范
### 事件命名规则
- 使用 'echosync:' 前缀
- 使用 kebab-case 命名
- 事件名称要清晰表达意图


## 数据共享规范

### Model单例使用
- **获取实例**: 通过 `ModelName.getInstance()` 获取单例实例
- **数据更新**: Capture模块负责在关键事件时更新共享数据
- **数据访问**: 提供公共方法供其他模块访问数据


### 数据共享最佳实践
- **职责明确**: Capture模块负责数据的生成和更新
- **时机合适**: 在关键事件（如发送前）更新共享数据
- **接口清晰**: 提供明确的数据访问方法
- **避免污染**: 不要在Capture中处理其他模块的业务逻辑

## Service层使用规范

### 在Capture中使用Service
- **获取实例**: 通过 `ServiceName.getInstance()` 获取Service单例
- **业务调用**: 在适当的时机调用Service的业务方法
- **数据传递**: 将捕获的数据传递给Service进行处理
- **错误处理**: 处理Service方法可能抛出的异常

### Service调用原则
- **时机合适**: 在数据捕获完成后调用Service
- **数据完整**: 确保传递给Service的数据完整有效
- **异步处理**: Service方法通常是异步的，需要正确处理Promise
- **错误隔离**: Service的错误不应影响Capture的核心功能

## 开发检查清单

### 新建 Capture 组件时检查
- [ ] 继承 BaseCapture 或遵循相同模式
- [ ] 通过构造函数接收 BaseAIAdapter 实例
- [ ] 实现 captureElement() 方法
- [ ] 实现 initEventListener() 方法
- [ ] 实现 destroy() 方法
- [ ] 使用 DOMUtils.findElement 查找元素
- [ ] 通过自定义事件与其他组件通信
- [ ] 正确使用Model单例进行数据共享
- [ ] 添加适当的错误处理和日志
- [ ] 文件大小不超过 300 行
- [ ] 遵循命名规范
