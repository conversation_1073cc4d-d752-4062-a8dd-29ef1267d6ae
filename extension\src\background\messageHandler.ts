/**
 * Background消息处理模块
 * 负责处理来自content script和popup的消息
 */

import { chatHistoryService } from '@/common/service/ChatHistoryService'
import { chatPromptService } from '@/common/service/ChatPromptService'
import { platformService } from '@/common/service/PlatformService'
import { databaseViewService } from '@/common/service/DatabaseViewService'
import { StorageService } from '@/common/service/storage'
import { MessageType, ChromeMessage } from '@/common/types'
import { databaseConnectionManager } from './databaseConnection'
import { healthMonitor } from './healthMonitor'

export class MessageHandler {
  /**
   * 处理消息的主要方法
   */
  static async handleMessage(
    message: ChromeMessage, 
    sender: chrome.runtime.MessageSender, 
    sendResponse: (response: any) => void
  ): Promise<void> {
    console.log('【Background】收到消息:', message.type)

    try {
      // 确保数据库连接就绪
      const isReady = await databaseConnectionManager.ensureConnection()
      if (!isReady) {
        throw new Error('Database connection not ready')
      }

      switch (message.type) {
        // 新建聊天提示词
        case MessageType.DB_CHAT_PROMPT_CREATE:
          const createResult = await chatPromptService.createPrompt(message.payload)
          sendResponse(createResult)
          break
        // 获得提示词列表
        case MessageType.DB_CHAT_PROMPT_LIST_GET:
          const uniqueResult = await chatPromptService.getChatPromptList(message.payload)
          sendResponse(uniqueResult)
          break

        case MessageType.DB_PLATFORM_GET_LIST:
          const platformListResult = await platformService.findAllActive()
          sendResponse(platformListResult)
          break

        // 聊天历史相关操作
        case MessageType.DB_CHAT_HISTORY_CREATE:
          const createHistoryResult = await chatHistoryService.createChatHistory(message.payload)
          sendResponse(createHistoryResult)
          break

        // 数据库查看相关操作
        case MessageType.DB_GET_ALL_TABLES:
          const tablesResult = await databaseViewService.getAllTables()
          sendResponse(tablesResult)
          break

        case MessageType.DB_GET_TABLE_DATA:
          const tableDataResult = await databaseViewService.getTableData(
            message.payload.tableName,
            message.payload.page,
            message.payload.limit
          )
          sendResponse(tableDataResult)
          break

        case MessageType.DB_GET_TABLE_COUNT:
          const tableCountResult = await databaseViewService.getTableCount(message.payload.tableName)
          sendResponse(tableCountResult)
          break

        case MessageType.DB_DELETE_RECORD:
          const recordDeleteResult = await databaseViewService.deleteRecord(
            message.payload.tableName,
            message.payload.recordId
          )
          sendResponse(recordDeleteResult)
          break

        case MessageType.DB_CLEAR_TABLE:
          const clearTableResult = await databaseViewService.clearTable(message.payload.tableName)
          sendResponse(clearTableResult)
          break
        case MessageType.UPDATE_SETTINGS:
          await StorageService.saveSettings(message.payload)
          sendResponse({ success: true })
          break

        // Favicon相关操作
        case MessageType.UPDATE_PLATFORM_FAVICON:
          await this.handleUpdatePlatformFavicon(message.payload)
          sendResponse({ success: true })
          break

        case MessageType.GET_SETTINGS:
          try {
            const settings = await StorageService.getSettings()
            sendResponse({ success: true, data: settings })
          } catch (error) {
            console.error('获取设置失败:', error)
            sendResponse({ success: false, error: '获取设置失败' })
          }
          break
          
        default:
          MessageHandler.handleSystemMessages(message, sendResponse)
      }
    } catch (error) {
      console.error('【Background】消息处理错误:', error)
      sendResponse({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

  /**
   * 处理系统消息
   */
  private static handleSystemMessages(message: any, sendResponse: (response: any) => void): void {
    switch (message.type) {
      case 'SW_PING':
        sendResponse({ success: true, timestamp: Date.now(), status: 'alive' })
        break
      case 'SW_WAKE_UP':
        console.log('【Background】Service Worker唤醒信号')
        sendResponse({ success: true, timestamp: Date.now(), status: 'awake' })
        break
      case 'SW_HEALTH_CHECK':
        healthMonitor.performHealthCheck().then(healthStatus => {
          sendResponse({ success: true, data: healthStatus })
        })
        break
      case 'SW_SYSTEM_STATUS':
        healthMonitor.getSystemStatus().then(systemStatus => {
          sendResponse({ success: true, data: systemStatus })
        })
        break
      default:
        sendResponse({ success: false, error: 'Unknown message type' })
    }
  }

  /**
   * 处理平台favicon更新
   */
  private static async handleUpdatePlatformFavicon(payload: {
    platformId: number
    faviconBase64: string
    faviconUrl?: string
  }): Promise<void> {
    try {
      console.log(`【Background-平台${payload.platformId}】更新favicon:`, {
        base64Length: payload.faviconBase64.length,
        url: payload.faviconUrl
      })

      const updateData: any = { icon_base64: payload.faviconBase64 }
      if (payload.faviconUrl) {
        updateData.icon = payload.faviconUrl
      }

      const result = await platformService.update(payload.platformId, updateData)
      if (result.success) {
        console.log(`【Background-平台${payload.platformId}】favicon更新成功`)
      } else {
        console.error(`【Background-平台${payload.platformId}】favicon更新失败:`, result.error)
      }
    } catch (error) {
      console.error(`【Background-平台${payload.platformId}】favicon更新异常:`, error)
    }
  }

}
