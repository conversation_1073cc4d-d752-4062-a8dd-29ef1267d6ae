---
type: "agent_requested"
description: "Background模块开发规则"
---

# Background模块开发规则

## 模块职责
Background Script是Chrome插件的后台服务模块，作为Service Worker运行，负责处理插件的核心业务逻辑和数据管理。

## 核心设计原则

### 模块特点
- **Service Worker**: 基于事件驱动的后台服务
- **消息中心**: 处理来自Content、Popup、Options的消息
- **数据管理**: 统一的数据库操作和存储管理
- **生命周期**: 管理插件的启动、运行和保活

### 架构要求
- 遵循Chrome Extension Manifest V3规范
- 使用消息驱动架构处理跨模块通信
- 实现统一的错误处理和日志记录
- 支持Service Worker的保活机制

## 项目结构规范

### 目录组织
```
background/
├── index.ts                 # 主入口文件，最多20行
├── messageHandler.ts        # 消息处理中心
├── eventListeners.ts        # Chrome扩展事件监听
├── databaseConnection.ts    # 数据库连接管理
├── keepAlive.ts            # Service Worker保活
└── healthMonitor.ts        # 系统健康监控
```

### 文件职责分工
- **index.ts**: 协调各模块初始化，不包含具体业务逻辑
- **messageHandler.ts**: 路由和处理所有消息类型
- **eventListeners.ts**: 监听Chrome扩展生命周期事件
- **databaseConnection.ts**: 管理数据库连接状态和初始化
- **keepAlive.ts**: 实现Service Worker保活策略
- **healthMonitor.ts**: 监控系统状态和性能

## 消息处理架构

### 消息路由规则
```typescript
// 消息处理流程
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  // 1. 验证消息格式
  // 2. 确保数据库连接
  // 3. 路由到对应服务
  // 4. 统一错误处理
  // 5. 返回标准格式结果
});
```

### 支持的消息类型
- **数据库操作**: DB_* 前缀的消息类型
- **配置管理**: CONFIG_* 前缀的消息类型
- **权限管理**: PERMISSION_* 前缀的消息类型
- **系统状态**: STATUS_* 前缀的消息类型

### 消息处理规范
- 所有消息必须返回DatabaseResult格式
- 实现统一的错误处理和日志记录
- 支持异步操作和超时处理
- 提供消息处理的性能监控

## 数据库集成

### 连接管理
- 延迟连接：首次消息时才初始化数据库
- 连接检查：每次操作前验证连接状态
- 错误恢复：连接失败时的重试和降级策略
- 资源清理：适当时机清理数据库连接

### 操作规范
- 严格遵循 database.md 中定义的分层架构
- 通过Service层处理所有数据库操作
- 使用统一的DatabaseResult格式返回结果
- 实现事务管理和错误回滚

### 生命周期管理
- 监听Service Worker启动和停止事件
- 实现优雅的关闭和重启机制
- 处理数据持久化和状态恢复
- 提供健康检查和状态报告

## 事件监听管理

### Chrome扩展事件
- **chrome.runtime.onInstalled**: 插件安装和更新
- **chrome.tabs.onUpdated**: 标签页更新
- **chrome.storage.onChanged**: 存储变化
- **chrome.permissions.onAdded/onRemoved**: 权限变化

### 事件处理规范
- 使用统一的事件处理器注册
- 实现事件的防抖和节流
- 提供事件处理的错误恢复
- 记录关键事件的日志信息


### 日志规范
```typescript
// 统一日志格式
console.log('【Background】[MessageHandler] 处理消息:', messageType);
console.error('【Background】[Database] 连接失败:', error);
console.warn('【Background】[KeepAlive] Service Worker重启');
```

## 相关规则文件

- **[数据库操作规则](./database.md)**: 数据库操作流程和规范
