import { BaseAIAdapter } from './BaseAIAdapter'

import { ChatGPTConfig } from '../types/Consts'
import { SelectorConfig } from '../types/PlatformConfigType'


export class ChatGPTAdapter extends BaseAIAdapter {
  constructor() {
    super(ChatGPTConfig)
  }

  /**
   * 获取 ChatGPT 平台特定的选择器配置
   */
  getSelectors(): SelectorConfig {
    return {
      inputField: [
        '#prompt-textarea',
        'textarea[placeholder*="Message"]'
      ],
      sendButton: [
        'button[data-testid="send-button"]',
        'button[aria-label="Send prompt"]'
      ]
    }
  }


}
