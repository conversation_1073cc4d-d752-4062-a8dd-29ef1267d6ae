---
type: "development_rules"
description: "UI注入组件开发规则"
---

# UI注入组件开发规则

## 核心设计原则

### 职责分离
- **Inject 模块**: 负责UI的创建、注入、销毁和用户交互
- **Component 模块**: 负责UI的渲染和样式
- **Service 模块**: 负责复杂的业务逻辑处理

### 非侵入性原则
- 不影响原页面的正常功能
- 不破坏原页面的样式和布局
- 提供清晰的视觉反馈
- 使用最高 z-index 确保显示层级

## Inject 组件开发规范

### 基础结构要求
- **位置**: `extension/src/content/inject/`
- **命名**: `ComponentNameInject.ts`
- **依赖**: 通过构造函数接收 BaseAIAdapter 实例
- **组件**: 创建对应的 Component 实例

### 必须实现的方法
- **inject()**: 注入组件到页面DOM
- **setupEventListeners()**: 设置全局事件监听
- **destroy()**: 销毁组件和清理资源

### 注入流程规范
1. 检查是否已注入，避免重复注入
2. 调用组件的 render() 方法获取DOM元素
3. 将元素添加到父容器
4. 设置事件监听器


## Component 组件开发规范

### 基础结构要求
- **位置**: `extension/src/content/components/`
- **命名**: `ComponentName.ts`
- **状态管理**: 维护 element 和 isRendered 状态
- **渲染机制**: 实现 render() 方法返回 DOM 元素
- **样式应用**: 通过 applyStyles() 设置内联样式
- **模板系统**: 通过 getTemplate() 返回 HTML 模板
- **生命周期**: 提供 destroy() 方法清理资源

## Service层使用规范

### 何时使用Service
- **复杂业务逻辑**: 当Inject模块的业务逻辑超过50行时
- **共享逻辑**: 当多个Inject模块需要相同的业务处理时
- **数据处理**: 当需要复杂的数据转换或验证时
- **外部API调用**: 当需要与Background或其他服务通信时

### Inject与Service的职责分工
- **Inject职责**: UI交互、事件处理、用户反馈、DOM操作
- **Service职责**: 业务逻辑、数据处理、API调用、状态管理
- **协作方式**: Inject调用Service方法，Service返回处理结果

## 样式和CSS规范

### CSS类命名规范
- 使用统一的 `echosync-` 前缀避免样式冲突
- 组件类: `echosync-floating-bubble`, `echosync-history-bubble`
- 状态类: `echosync-bubble--hover`, `echosync-bubble--dragging`

### 样式隔离策略
- 优先使用内联样式避免样式冲突
- 设置最高 z-index (2147483647) 确保显示层级
- 禁用用户选择和指针事件控制
- 使用 CSS-in-JS 方式应用样式

### 响应式设计要求
- 检测视口尺寸进行适配
- 移动端和桌面端差异化处理
- 动态调整组件大小和位置


## 数据共享规范

### Model单例使用
- **获取实例**: 通过 `ModelName.getInstance()` 获取单例实例
- **数据消费**: Inject模块负责消费Capture模块生成的共享数据
- **数据访问**: 通过Model实例的公共方法获取数据

## 开发检查清单

### 新建 Inject 组件时检查:
- [ ] 通过构造函数接收 BaseAIAdapter 实例
- [ ] 实现 inject() 方法
- [ ] 实现 destroy() 方法
- [ ] 创建对应的 Component 类
- [ ] 设置适当的事件监听器
- [ ] 正确使用Model单例获取共享数据
- [ ] 合理使用Service处理复杂业务逻辑
- [ ] 使用统一的CSS类命名
- [ ] 添加适当的错误处理
- [ ] 文件大小不超过 300 行
- [ ] 遵循命名规范

