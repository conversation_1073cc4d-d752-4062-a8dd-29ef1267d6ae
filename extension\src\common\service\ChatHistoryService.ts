import { chatHistoryDao } from '../dao/ChatHistoryDao'
import { chatPromptDao } from '../dao/ChatPromptDao'
import { platformDao } from '../dao/PlatformDao'
import { ChatHistoryEntity, ChatPromptEntity, PlatformEntity } from '@/common/types/database_entity'
import {
  ChatPromptListResp,
} from '@/common/types/content_vo'
import {
  DatabaseResult,
} from '@/common/types/comm_vo'
import { Singleton } from '../base'

/**
 * 聊天历史服务
 * 负责业务逻辑处理和Entity到DTO的转换
 */
export class ChatHistoryService extends Singleton<ChatHistoryService> {

  /**
   * 创建聊天历史记录
   */
  async createChatHistory(data: {
    prompt_uid: string
    platform_id: number
    chat_answer: string
    chat_group_name?: string
    chat_sort?: number
    p_uid?: string
    create_time?: number
  }): Promise<DatabaseResult<ChatHistoryEntity>> {
    try {
      // 数据验证
      if (!data.prompt_uid || data.prompt_uid.trim().length === 0) {
        return {
          success: false,
          error: 'Prompt UID is required'
        }
      }

      if (!data.platform_id || data.platform_id <= 0) {
        return {
          success: false,
          error: 'Valid platform ID is required'
        }
      }

      if (!data.chat_answer || data.chat_answer.trim().length === 0) {
        return {
          success: false,
          error: 'Chat answer is required'
        }
      }

      // 检查平台是否存在
      const platform = await platformDao.findById(data.platform_id)
      if (!platform) {
        return {
          success: false,
          error: `Platform with ID ${data.platform_id} not found`
        }
      }

      // 创建聊天历史记录
      const chatHistory = await chatHistoryDao.create({
        prompt_uid: data.prompt_uid,
        platform_id: data.platform_id,
        chat_answer: data.chat_answer,
        chat_group_name: data.chat_group_name,
        chat_sort: data.chat_sort,
        p_uid: data.p_uid,
        create_time: data.create_time || Date.now(),
        is_synced: 0,
        is_answered: 1,
        is_delete: 0
      })

      console.log('【ChatHistoryService】Chat history created successfully:', chatHistory)

      return {
        success: true,
        data: chatHistory
      }
    } catch (error) {
      console.error('【ChatHistoryService】Create chat history error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 将Entity转换为DTO
   */
  private async convertToDto(chatHistory: ChatHistoryEntity): Promise<ChatPromptListResp> {
    // 获取平台信息
    const platform = await platformDao.findById(chatHistory.platform_id)

    return {
      id: chatHistory.id,
      prompt_uid: chatHistory.prompt_uid,
      chat_prompt: '', // 这里需要从ChatPrompt表获取
      platform_ids: platform ? platform.id!.toString() : '',
      platform_info: platform ? [{
        platform_id: platform.id!,
        platform_url: platform.url,
        platform_name: platform.name,
        platform_icon: platform.icon,
        platform_icon_base64: platform.icon_base64
      }] : [],
      create_time: chatHistory.create_time,
      is_synced: chatHistory.is_synced,
      is_delete: chatHistory.is_delete
    }
  }

  /**
   * 将Entity转换为DTO（带平台和提示词信息）
   */
  private async convertToDtoWithPrompt(chatHistory: ChatHistoryEntity, chatPrompt: string): Promise<ChatPromptListResp> {
    const baseDto = await this.convertToDto(chatHistory)
    
    return {
      ...baseDto,
      chat_prompt: chatPrompt
    }
  }


  /**
   * 根据prompt_uid删除所有相关记录
   */
  async deleteByPromptUid(promptUid: string): Promise<DatabaseResult<number>> {
    try {
      const count = await chatHistoryDao.softDeleteByPromptUid(promptUid)
      return {
        success: true,
        data: count
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 获取统计信息
   */
  async getStats(): Promise<DatabaseResult<{
    total: number
    byPlatform: { [platformName: string]: number }
  }>> {
    try {
      const total = await chatHistoryDao.count()
      const platforms = await platformDao.findAllActive()
      const byPlatform: { [platformName: string]: number } = {}

      for (const platform of platforms) {
        const count = await chatHistoryDao.count({ platformId: platform.id })
        byPlatform[platform.name] = count
      }

      return {
        success: true,
        data: {
          total,
          byPlatform
        }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }
  
}

// 导出单例实例
export const chatHistoryService = ChatHistoryService.getInstance()
