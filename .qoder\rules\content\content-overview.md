---
type: "development_rules"
description: "Content Script模块总览和架构规则"
---

# Content Script模块总览

## 模块职责
Content Script是Chrome插件的核心模块，负责在网页中注入功能和UI元素，实现与AI平台的交互。

## 核心架构

### 中介者模式设计
- **BaseAIAdapter**: 作为唯一中介者，协调所有子模块
- **平台适配器**: 继承BaseAIAdapter，实现平台特定逻辑
- **功能模块**: 通过中介者进行通信，禁止直接交互

### 模块组成
```
content/
├── adapters/               # 平台适配器
│   ├── BaseAIAdapter.ts   # 中介者基类
│   ├── chatgpt.ts         # ChatGPT适配器
│   ├── claude.ts          # Claude适配器
│   ├── deepseek.ts        # DeepSeek适配器
│   ├── gemini.ts          # Gemini适配器
│   └── kimi.ts            # Kimi适配器
├── capture/               # 页面元素捕捉
│   ├── BaseCapture.ts     # 捕捉基类
│   ├── InputCapture.ts    # 输入框捕捉
│   ├── FaviconCapture.ts  # 网站图标捕捉
│   └── PlatformDetector.ts # 平台检测
├── inject/                # UI注入模块
│   ├── FloatingBubbleInject.ts      # 浮动小球注入
│   ├── ArchiveButtonInject.ts       # 存档按钮注入
│   ├── FloatingBubbleDragService.ts # 浮动小球拖拽服务
│   └── HistoryBubbleService.ts      # 历史记录气泡服务
├── service/               # 共享业务服务层
│   └── ArchiveService.ts  # 存档业务服务
├── model/                 # 数据模型层
│   └── InputModel.ts      # 输入数据模型单例
├── components/            # Content专用UI组件
│   ├── FloatingBubble.ts  # 浮动小球组件
│   ├── HistoryBubble.ts   # 历史记录气泡组件
│   ├── HistoryBubble.css  # 历史记录样式
│   ├── ArchiveButton.ts   # 存档按钮组件
│   ├── PlatformIcon.ts    # 平台图标组件
│   └── Toast.ts           # Toast提示组件
├── configs/               # 配置文件
│   ├── Consts.ts          # 常量配置
│   ├── CommonSelectors.ts # 通用选择器
│   └── DOMEnum.ts         # DOM枚举
├── types/                 # 类型定义
│   └── PlatformConfigType.ts # 平台配置类型
├── utils/                 # 工具函数
│   ├── DOMUtils.ts        # DOM操作工具
│   └── PerformanceOptimizer.ts # 性能优化工具
├── ContentScriptManager.ts # Content Script管理器
└── index.ts              # 入口文件
```

## 设计原则

### 单一职责
- **Adapter**: 平台适配和组件协调
- **Capture**: 页面元素捕捉和监听
- **Inject**: UI元素注入和交互
- **Service**: 共享业务逻辑和数据处理
- **Model**: 数据模型和状态管理
- **Component**: UI组件渲染和样式

### 依赖注入
- 所有子模块通过构造函数接收adapter实例
- 避免模块间直接依赖
- 通过中介者访问共享资源

### 事件驱动
- 使用`document.dispatchEvent`进行多个模块间通信
- 统一的事件命名规范（echosync:前缀）
- 事件监听器的生命周期管理

### 数据共享模式
- 使用Model单例模式管理共享数据
- Capture和Inject模块通过Model实例共享状态
- 避免模块间直接数据传递

### Service层设计原则
- **共享业务逻辑**: 将inject和capture模块共享的业务逻辑抽取到service层
- **单例模式**: Service类继承Singleton基类，确保全局唯一实例
- **依赖注入**: Service可以依赖Model、DatabaseProxy等其他服务
- **职责分离**: Service专注业务逻辑，不处理UI交互和DOM操作

## 数据流向

### 交互流程
1. Capture模块监听页面变化
2. 通过事件通知各个Inject组件
3. UI组件处理用户交互
4. 通过各种`DatabaseProxy`与Background通信

## 平台适配规则

### 适配器实现要求
- 必须继承BaseAIAdapter
- 实现getSelectors()抽象方法
- 提供平台特定的选择器配置
- 可重写事件处理方法

### 选择器管理
- 平台选择器优先级高于通用选择器
- 通过mergeSelectors()方法统一合并
- 支持动态选择器更新

## 开发规范

### 新增平台适配器
1. 在adapters/目录创建平台文件
2. 继承BaseAIAdapter类
3. 实现getSelectors()方法
4. 在ContentScriptManager中注册

### 新增功能模块
1. 确定模块类型（Capture/Inject/Service/Component）
2. 在对应目录创建文件
3. 通过构造函数接收adapter依赖
4. 实现标准的生命周期方法

### 新增Service服务
1. 在service/目录创建服务文件
2. 继承Singleton基类实现单例模式
3. 定义清晰的业务接口方法
4. 在inject或capture模块中通过getInstance()使用

## 数据共享模式规范

### Model单例设计原则
- **位置**: `content/model/` 目录
- **命名**: `ModelName.ts`，如 `InputModel.ts`
- **实现**: 使用单例模式，提供 `getInstance()` 静态方法
- **职责**: 管理模块间共享的数据状态


### 模块间数据共享规则
1. **Capture模块**: 负责数据的生成和更新
   - 在关键事件时更新Model数据

2. **Inject模块**: 负责数据的消费和使用
   - 通过Model实例获取共享数据

3. **数据访问**: 统一通过Model实例的公共方法
   - 避免直接访问私有属性
   - 提供明确的数据操作接口


### 数据共享最佳实践
- **最小化共享**: 只共享必要的数据，避免过度耦合
- **明确职责**: 明确哪个模块负责数据的生成和更新
- **接口设计**: 提供清晰的数据访问接口
- **生命周期**: 考虑数据的生命周期和清理时机
- **线程安全**: 虽然JavaScript是单线程，但要考虑异步操作的数据一致性


## 相关规则文件

- **[中介者模式规则](./mediator.md)**: BaseAIAdapter实现和选择器管理
- **[页面捕捉规则](./capture.md)**: Capture模块开发规范
- **[UI注入规则](./inject.md)**: Inject模块开发规范
- **[Service层规则](./service.md)**: Service业务逻辑层开发规范
- **[数据模型规则](./model.md)**: Model单例和数据共享规范