---
type: "agent_requested"
description: "数据库操作流程和规则"
---

# 数据库操作规则

## 目录
```
src/common 
├── dao/             # 数据访问对象层
│   ├── ChatHistoryDao.ts
│   ├── ChatPromptDao.ts
│   └── PlatformDao.ts
├── database/      # 数据库层
│   ├── dexie.ts   # Dexie数据库配置
│   └── index.ts   # 数据库实例导出
├── service/       # 业务服务层
│   ├── ChatHistoryService.ts
│   ├── ChatPromptService.ts
│   └── PlatformService.ts
├── types/         # 类型定义层
│   ├── database_entity.ts   # 数据库实体类型
│   ├── database_vo.ts      # 数据传输对象类型
│   ├── enums.ts             # 枚举类型定义
│   └── index.ts             # 类型导出入口
├── utils.ts       # 通用工具函数
```

## 核心原则

### 严格的分层架构
数据库访问必须遵循以下层次：
**Content Script → MessagingService → Background → common/Service → common/DAO → common/Database**

### 禁止行为
- Content Script 直接访问数据库
- UI组件直接调用 DAO 层
- 跨层级的直接调用

## 消息驱动的数据库操作

### 消息类型定义
所有数据库操作必须定义对应的 `enums.ts` 中的 MessageType 枚举值：
- **平台相关**: DB_PLATFORM_GET_LIST 等

### 标准消息格式
- **ChromeMessage**: 包含 type, payload, timestamp
- **DatabaseResult**: 包含 success, data, error

### Content Script 发送消息规则
- 必须使用 MessagingService.senvoBackground 方法
- 必须指定正确的 MessageType
- 必须处理返回的 DatabaseResult 格式
- 必须进行错误处理

## Background 消息处理

### MessageHandler 路由规则
- **位置**: `extension/src/background/messageHandler.ts`
- **职责**: 接收消息并路由到对应的 Service
- **流程**:
  1. 确保数据库连接就绪
  2. 根据消息类型路由到对应服务
  3. 统一错误处理和响应格式
  4. 使用 sendResponse 返回结果

### 数据库连接管理
- **位置**: `extension/src/background/databaseConnection.ts`
- **职责**: 管理数据库连接的生命周期
- **功能**:
  - 检查连接状态
  - 按需初始化数据库
  - 连接异常处理

## Service 层规则

### Service 类设计
- **位置**: `extension/src/common/service/`
- **命名**: `EntityNameService.ts`
- **实现**: 单例模式，导出实例而非类
- **返回格式**: 统一使用 DatabaseResult 格式

### Service 层职责
- **业务逻辑处理**: 协调多个 DAO 完成复杂业务
- **事务管理**: 处理需要事务的复杂操作
- **错误处理**: 统一的错误处理和日志记录
- **消息响应**: 处理来自 Background 的消息

## DAO 层规则

### DAO 类设计
- **位置**: `extension/src/common/dao/`
- **命名**: `EntityNameDao.ts`
- **实现**: 单例模式，导出实例而非类
- **依赖**: 直接使用 dexieDatabase 实例

### DAO 层职责
- **纯数据操作**: 只处理数据库的 CRUD 操作
- **查询优化**: 使用适当的索引和查询策略
- **数据映射**: 在数据库格式和业务对象间转换
- **无业务逻辑**: 不包含任何业务规则
- **单表操作**: 每个 DAO 只负责一个数据表

## 数据库层规则

### Dexie 数据库定义
- **位置**: `extension/src/common/database/dexie.ts`
- **实现**: 继承 Dexie 类，定义表结构和索引
- **导出**: 导出数据库实例，供 DAO 层使用
- **命名**: 数据库名称使用 'EchoSyncDB'

### 数据库初始化
- **按需初始化**: 首次消息时才连接数据库
- **版本管理**: 使用 Dexie 的版本管理机制
- **错误处理**: 连接失败时的降级处理

## Entity

### Entity 类型定义
- **位置**: `extension/src/common/types/database_entity.ts`
- **命名**: `EntityNameEntity` 格式
- **属性**: 与数据库表结构完全对应
- **类型**: 使用 TypeScript interface 或 type

### Entity 设计规则
- **必需字段**: 
  - id: 主键字段
  - createTime: 创建时间
  - updateTime: 更新时间
- **字段类型**: 使用明确的 TypeScript 类型
- **字段注释**: 为每个字段添加 JSDoc 注释
- **索引字段**: 标注哪些字段需要建立索引

### Entity 使用规范
- **只在 DAO 层使用**: Entity 仅用于数据库操作
- **不包含方法**: Entity 只定义数据结构
- **不可变性**: 所有字段设为只读
- **类型导出**: 通过 index.ts 统一导出


## VO
返回给其他前端模块（content,popup,options）的数据结构
### VO 类型定义
- **位置**: `extension/src/common/types/database_vo.ts`
- **命名**: `EntityNameVO` 格式
- **属性**: 只包含业务所需的字段
- **类型**: 使用 TypeScript interface 或 type

### vo 设计规则
- **数据转换**: 提供 Entity 与 VO 间的转换方法
- **字段筛选**: 只暴露必要的业务字段
- **验证规则**: 包含数据验证逻辑

### vo 使用规范
- **Service 层使用**: vo 用于业务逻辑处理
- **接口交互**: 用于前后台通信的数据格式
- **类型导出**: 通过 index.ts 统一导出


## 错误处理规范

### 统一错误格式
- **DatabaseResult**: 统一的服务返回格式
- **错误信息**: 包含 success, data, error 字段
- **错误传播**: 底层错误向上传播到 Service 层处理

### 错误处理策略
- **Service 层**: 捕获并转换为 DatabaseResult 格式
- **DAO 层**: 让错误向上传播，由 Service 层处理
- **Background**: 统一的 try-catch 和错误响应

## 开发规范

### 新增数据表流程
1. 在 dexie.ts 中定义表结构
2. 创建对应的 Entity 类型
3. 创建对应的 DAO 类
4. 创建对应的 Service 方法
5. 在 MessageHandler 中添加路由

### 新增查询方法流程
1. 在 DAO 中添加查询方法
2. 在 Service 中添加业务方法
3. 定义新的 MessageType
4. 在 MessageHandler 中添加处理
