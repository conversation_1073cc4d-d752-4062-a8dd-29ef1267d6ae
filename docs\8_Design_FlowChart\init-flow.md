# EchoAI 插件启动和初始化流程图

本文档详细描述了 EchoAI Chrome 插件从安装到完全初始化的完整流程，**基于实际代码实现**。

> **重要说明**: 本文档已根据实际代码进行修正，移除了虚构的流程和组件，所有流程节点都对应真实的类和方法。

## 一、整体启动流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant C as Chrome浏览器
    participant BI as background/index.ts
    participant EL as EventListeners
    participant MH as MessageHandler
    participant CS as ContentScriptManager
    participant PD as PlatformDetector
    participant BA as BaseAIAdapter

    U->>C: 安装/启动插件
    C->>BI: Service Worker启动
    BI->>EL: EventListeners.initialize()
    BI->>MH: MessagingService.onMessage(MessageHandler.handleMessage)

    Note over BI: Background 初始化完成

    U->>C: 访问AI网站
    C->>CS: 注入 Content Script
    CS->>PD: new PlatformDetector()
    PD->>PD: detectCurrentPlatform()
    PD-->>CS: 返回 PlatformConfig

    alt 支持的平台
        CS->>CS: createAdapter(platformId)
        CS->>BA: new ChatGPTAdapter() 等
        BA->>BA: mergeSelectors()
        BA->>BA: 初始化 InputCapture
        BA->>BA: 初始化 FloatingBubbleInject
        BA->>BA: 初始化 ArchiveButtonInject
        CS->>CS: loadPlatformInfo()
        Note over CS: Content Script 完全初始化
    else 不支持的平台
        CS->>CS: 不执行任何操作
    end
```

## 二、Background Script 详细初始化流程

```mermaid
flowchart TD
    A[Chrome 启动插件] --> B[background/index.ts 加载]
    B --> C[console.log 启动日志]
    C --> D[EventListeners.initialize]
    C --> E[MessagingService.onMessage]
    C --> F[console.log 初始化完成]

    D --> D1[setupInstallListener]
    D --> D2[setupTabUpdateListener]
    D --> D3[setupCommandListener]
    D --> D4[setupStartupListener]

    E --> E1[MessageHandler.handleMessage 注册]

    D2 --> D2A[handleTabComplete]
    D2A --> D2B[检查支持的域名]
    D2B --> D2C[chrome.scripting.executeScript]

    F --> G[Background 初始化完成]

    Note1[数据库连接在首次消息时初始化]
    Note2[KeepAlive 在需要时启动]
    Note3[HealthMonitor 按需调用]
```

## 三、Content Script 详细初始化流程

```mermaid
flowchart TD
    A[页面加载完成] --> B[content/index.ts 执行]
    B --> C[new ContentScriptManager]
    C --> D[new PlatformDetector]
    D --> E[detectCurrentPlatform]

    E --> F{PlatformConfig 存在?}
    F -->|否| G[不执行任何操作]
    F -->|是| H[createAdapter]

    H --> I{platformId 匹配}
    I -->|chatgpt| J[new ChatGPTAdapter]
    I -->|claude| K[new ClaudeAdapter]
    I -->|deepseek| L[new DeepSeekAdapter]
    I -->|其他| M[对应 Adapter]

    J --> N[BaseAIAdapter 构造函数]
    K --> N
    L --> N
    M --> N

    N --> O[mergeSelectors]
    O --> O1[getSelectors 获取平台选择器]
    O --> O2[CommonSelectors 通用选择器]
    O1 --> O3[合并到 mergedSelectors]
    O2 --> O3

    O3 --> P[initCapture]
    P --> P1[new InputCapture]

    O3 --> Q[initInject]
    Q --> Q1[new FloatingBubbleInject]
    Q --> Q2[new ArchiveButtonInject]

    P1 --> R[loadPlatformInfo]
    Q1 --> R
    Q2 --> R

    R --> S[platformDatabaseProxy.getAll]
    S --> T[checkAndUpdateFavicon]
    T --> U[adapter.setCurrentPlatform]
    U --> V[Content Script 初始化完成]
```

## 四、数据库初始化流程（按需初始化）

```mermaid
flowchart TD
    A[首次消息到达] --> B[MessageHandler.handleMessage]
    B --> C[databaseConnectionManager.ensureConnection]
    C --> D{数据库已连接?}

    D -->|是| E[直接处理消息]
    D -->|否| F[初始化数据库连接]

    F --> G[创建 Dexie 实例]
    G --> H[定义表结构]
    H --> H1[chat_history 表]
    H --> H2[platforms 表]

    H1 --> I[打开数据库]
    H2 --> I

    I --> J{打开成功?}
    J -->|是| K[设置连接状态为就绪]
    J -->|否| L[抛出连接错误]

    K --> E
    L --> M[返回错误响应]

    Note1[数据库在首次使用时才初始化]
    Note2[不预先插入默认数据]
    Note3[平台数据通过 Service 动态创建]
```

## 五、基本错误处理

```mermaid
flowchart TD
    A[初始化过程中发生错误] --> B[console.error 记录错误]
    B --> C{错误类型}

    C -->|平台检测失败| D[不执行任何操作]
    C -->|选择器查找失败| E[console.warn 警告]
    C -->|组件初始化失败| F[跳过失败组件]
    C -->|数据库连接失败| G[返回错误响应]

    D --> H[静默失败]
    E --> I[部分功能不可用]
    F --> I
    G --> J[消息处理失败]

    H --> K[初始化结束]
    I --> K
    J --> K
```

## 六、实际性能特点

### 实际实现特点
- Background Script: 仅18行代码，启动极快
- Content Script: 按需初始化，只在支持的网站运行
- 数据库连接: 延迟到首次使用时才连接
- 平台检测: 基于简单的域名正则匹配
- UI组件: 同步初始化，无复杂异步逻辑

### 设计优势
1. **简洁设计**: 避免过度工程化
2. **按需加载**: 数据库等资源按需初始化
3. **错误隔离**: 组件独立，单点失败不影响其他功能
4. **轻量级**: 最小化资源占用和启动时间
