import { Singleton } from "@/common/base/Singleton";
import ArchiveService from "../service/ArchiveService";
import { InputModel } from "../model/InputModel";
import { PlatformEntity } from "@/common/types/database_entity";
import { BaseAIAdapter } from "../adapters/BaseAIAdapter";
import { DOMUtils } from "../utils/DOMUtils";
import { BaseCapture } from "./BaseCapture";
import { EchoSyncEventEnum } from "../types/DOMEnum";
import { Select } from "@radix-ui/themes";
import SelectorManager from "../configs/SelectorManager";

/**
 * 发送按钮捕捉器
 * 负责捕捉用户发送问题的操作
 */
export class AskCapture extends BaseCapture {

  private archiveService: ArchiveService;

  // 状态管理
  private currentChatUid: string = '';
  private sendTimestamp: number = 0;

  // 发送按钮容器
  private sendButtonElement: HTMLButtonElement | null = null;
  private sendButtonClickHandler: (() => void) | null = null;
  private adapter: BaseAIAdapter = null;



  constructor(
    adapter: BaseAIAdapter,
    private inputModel: InputModel
  ) {
    super();
    this.adapter = adapter;
    this.inputModel = inputModel;
    this.archiveService = ArchiveService.getInstance();
    this.initEventListener();
  }

  protected initCaptureElement(): void {
    console.log('【AskAnswerCapture】不需要初始化元素');
  }

  protected initEventListener(): void {
    this.setupInputChangeListener();
  }

  /**
   * 设置发送按钮点击监听器
   */
  private setupSendButtonClickListener(): void {
    if (this.sendButtonElement) {
      // 创建点击处理函数
      this.sendButtonClickHandler = () => {
        // 检查按钮是否真的可用
        if (this.isButtonReallyEnabled()) {
          console.log('【AskAnswerCapture】Send button clicked and enabled, handling event');
          this.handleSendEvent();
        } else {
          console.log('【AskAnswerCapture】Send button clicked but disabled, ignoring');
        }
      };

      // 添加事件监听器
      this.sendButtonElement.addEventListener('click', this.sendButtonClickHandler);
      console.log('【AskAnswerCapture】Send listener set up');
    } else {
      console.warn('【AskAnswerCapture】Send button not found, cannot set up click listener');
    }
  }



  /**
   * 设置输入变化监听器
   * 监听InputCapture发送的INPUT_CHANGED事件，动态更新发送按钮绑定
   */
  private setupInputChangeListener(): void {
    document.addEventListener(EchoSyncEventEnum.INPUT_CHANGED, (event: CustomEvent) => {
      const { value } = event.detail;
      console.log('【AskAnswerCapture】Input changed, updating send button binding:', value);

      // 重新查找和绑定发送按钮
      this.updateSendButtonBinding();
    });

    console.log('【AskAnswerCapture】Input change listener setup complete');
  }

  /**
   * 更新发送按钮绑定
   * 重新查找发送按钮并更新事件监听器
   */
  private updateSendButtonBinding(): void {
    try {
      // 重新查找发送按钮
      const newSendButton = DOMUtils.findElement(SelectorManager.getSelector()?.sendButton) as HTMLButtonElement;

      if (newSendButton) {
        // 如果找到了新的按钮，且与当前按钮不同
        if (newSendButton !== this.sendButtonElement) {
          console.log('【AskAnswerCapture】Found new send button, updating binding');

          // 移除旧按钮的监听器（如果存在）
          if (this.sendButtonElement && this.sendButtonClickHandler) {
            this.sendButtonElement.removeEventListener('click', this.sendButtonClickHandler);
            console.log('【AskAnswerCapture】Removed old send button listener');
          }

          // 更新按钮引用
          this.sendButtonElement = newSendButton;

          // 设置新的监听器
          this.setupSendButtonClickListener();

          console.log('【AskAnswerCapture】Send button binding updated successfully');
        } else {
          console.log('【AskAnswerCapture】Send button unchanged, no update needed');
        }
      } else {
        console.warn('【AskAnswerCapture】No send button found during update');
        
        // 如果没有找到按钮，但之前有按钮，需要清理旧的监听器
        if (this.sendButtonElement && this.sendButtonClickHandler) {
          this.sendButtonElement.removeEventListener('click', this.sendButtonClickHandler);
          this.sendButtonElement = null;
          this.sendButtonClickHandler = null;
          console.log('【AskAnswerCapture】Cleared old send button binding');
        }
      }
    } catch (error) {
      console.error('【AskAnswerCapture】Error updating send button binding:', error);
    }
  }

  public isButtonEnable(): boolean {
    if (this.sendButtonElement && !this.sendButtonElement.disabled) {
      return true;
    }
    return false;
  }

  /**
   * 检查发送按钮是否真的可用（考虑容器状态）
   */
  private isButtonReallyEnabled(): boolean {
    if (!this.sendButtonElement) {
      return false;
    }

    // 检查按钮本身是否禁用
    if (this.sendButtonElement.disabled) {
      return false;
    }

    // 检查按钮容器是否有disabled类
    const container = this.sendButtonElement.closest('.send-button-container');
    if (container && container.classList.contains('disabled')) {
      return false;
    }

    // 检查按钮的pointer-events样式
    const computedStyle = window.getComputedStyle(this.sendButtonElement);
    if (computedStyle.pointerEvents === 'none') {
      return false;
    }

    return true;
  }


  /**
   * 处理发送事件
   */
  public async handleSendEvent(): Promise<void> {
    try {
      console.log('【AskAnswerCapture】Send event detected');

      this.currentChatUid = this.inputModel.getCurrPromptUid();
      this.sendTimestamp = Date.now();

      // 获取输入内容
      const inputPrompt = this.inputModel.getCurrPrompt();
      if (!inputPrompt || inputPrompt.trim().length === 0) {
        console.warn('【AskAnswerCapture】Input is empty, skipping archive');
        return;
      }

      // 触发事件通知其他组件
      this.notifyPromptSent();

      // 存档提示词
      const platform = this.adapter.getCurrentPlatform();
      if (platform) {
        await this.archiveService.archivePrompt(inputPrompt, platform);
        console.log('【AskAnswerCapture】Prompt archived successfully');
      }

    } catch (error) {
      console.error('【AskAnswerCapture】Failed to handle send event:', error);
    }
  }

  /**
   * 通知提示词已发送
   */
  private notifyPromptSent(): void {
    const event = new CustomEvent('echosync:prompt-sent', {
      detail: {
        chatUid: this.currentChatUid,
        timestamp: this.sendTimestamp
      }
    });
    document.dispatchEvent(event);
    console.log('【AskAnswerCapture】Prompt sent event dispatched');
  }



  /**
   * 销毁资源
   */
  public destroy(): void {
    console.log('【AskAnswerCapture】Destroying capture');

    // 移除发送按钮监听器
    if (this.sendButtonElement && this.sendButtonClickHandler) {
      this.sendButtonElement.removeEventListener('click', this.sendButtonClickHandler);
      this.sendButtonClickHandler = null;
    }

    // 重置状态
    this.currentChatUid = '';
    this.sendTimestamp = 0;

    console.log('【AskAnswerCapture】Capture destroyed');
  }
}
