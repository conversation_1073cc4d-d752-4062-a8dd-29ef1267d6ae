---
type: "development_rules"
description: "数据库操作流程和规则"
---

# 数据库操作规则

## 核心原则

### 严格的分层架构
数据库访问必须遵循以下层次：
**Content Script → MessagingService → Background → Service → DAO → Database**

### 禁止行为
- Content Script 直接访问数据库
- UI组件直接调用 DAO 层
- 跨层级的直接调用

## 消息驱动的数据库操作

### 消息类型定义
所有数据库操作必须定义对应的 MessageType 枚举值：
- **平台相关**: DB_PLATFORM_GET_LIST 等
- **位置**: `extension/src/common/types/enums.ts`

### 标准消息格式
- **ChromeMessage**: 包含 type, payload, timestamp
- **DatabaseResult**: 包含 success, data, error
- **位置**: `extension/src/common/types/index.ts`

### Content Script 发送消息规则
- 必须使用 MessagingService.sendToBackground 方法
- 必须指定正确的 MessageType
- 必须处理返回的 DatabaseResult 格式
- 必须进行错误处理

## Background 消息处理

### MessageHandler 路由规则
- **位置**: `extension/src/background/messageHandler.ts`
- **职责**: 接收消息并路由到对应的 Service
- **流程**:
  1. 确保数据库连接就绪
  2. 根据消息类型路由到对应服务
  3. 统一错误处理和响应格式
  4. 使用 sendResponse 返回结果

### 数据库连接管理
- **位置**: `extension/src/background/databaseConnection.ts`
- **职责**: 管理数据库连接的生命周期
- **功能**:
  - 检查连接状态
  - 按需初始化数据库
  - 连接异常处理

## Service 层规则

### Service 类设计
- **位置**: `extension/src/common/service/`
- **命名**: `EntityNameService.ts`
- **实现**: 单例模式，导出实例而非类
- **返回格式**: 统一使用 DatabaseResult 格式

### Service 层职责
- **业务逻辑处理**: 协调多个 DAO 完成复杂业务
- **数据验证**: 验证输入数据的有效性
- **事务管理**: 处理需要事务的复杂操作
- **错误处理**: 统一的错误处理和日志记录
- **消息响应**: 处理来自 Background 的消息

## DAO 层规则

### DAO 类设计
- **位置**: `extension/src/common/dao/`
- **命名**: `EntityNameDao.ts`
- **实现**: 单例模式，导出实例而非类
- **依赖**: 直接使用 dexieDatabase 实例

### DAO 层职责
- **纯数据操作**: 只处理数据库的 CRUD 操作
- **查询优化**: 使用适当的索引和查询策略
- **数据映射**: 在数据库格式和业务对象间转换
- **无业务逻辑**: 不包含任何业务规则
- **单表操作**: 每个 DAO 只负责一个数据表

## 数据库层规则

### Dexie 数据库定义
- **位置**: `extension/src/common/database/dexie.ts`
- **实现**: 继承 Dexie 类，定义表结构和索引
- **导出**: 导出数据库实例，供 DAO 层使用
- **命名**: 数据库名称使用 'EchoSyncDB'

### 数据库初始化
- **按需初始化**: 首次消息时才连接数据库
- **版本管理**: 使用 Dexie 的版本管理机制
- **错误处理**: 连接失败时的降级处理

## 错误处理规范

### 统一错误格式
- **DatabaseResult**: 统一的服务返回格式
- **错误信息**: 包含 success, data, error 字段
- **错误传播**: 底层错误向上传播到 Service 层处理

### 错误处理策略
- **Service 层**: 捕获并转换为 DatabaseResult 格式
- **DAO 层**: 让错误向上传播，由 Service 层处理
- **Background**: 统一的 try-catch 和错误响应

## 性能优化规则

### 查询优化
- 使用适当的索引提高查询性能
- 限制查询结果数量避免内存溢出
- 避免复杂的联表查询

### 连接管理
- 延迟连接：首次使用时才连接
- 连接复用：避免重复创建连接
- 健康检查：定期检查连接状态

### 数据缓存
- 避免频繁查询相同数据
- 合理使用内存缓存
- 及时清理过期缓存

## 开发规范

### 新增数据表流程
1. 在 dexie.ts 中定义表结构
2. 创建对应的 Entity 类型
3. 创建对应的 DAO 类
4. 创建对应的 Service 方法
5. 在 MessageHandler 中添加路由

### 新增查询方法流程
1. 在 DAO 中添加查询方法
2. 在 Service 中添加业务方法
3. 定义新的 MessageType
4. 在 MessageHandler 中添加处理

### 数据迁移流程
1. 增加 Dexie 版本号
2. 定义迁移逻辑
3. 测试数据迁移过程
4. 提供回滚方案

## 调试和监控

### 日志记录规范
- 使用统一的日志前缀 '【Database】'
- 记录操作类型和关键数据
- 使用 console.error 记录错误信息

### 性能监控
- 记录查询执行时间
- 监控数据库连接状态
- 统计操作成功率

### 开发工具
- 使用 Chrome DevTools 查看 IndexedDB
- 使用 Dexie 的调试功能
- 记录详细的操作日志
