import { BaseAIAdapter } from '@/content/adapters/BaseAIAdapter'
import { KimiConfig } from '@/content/types/Consts'
import { KimiAnswerController } from './KimiAnswerController'

export class KimiAdapter extends BaseAIAdapter {
  private kimiAnswerController: KimiAnswerController | null = null;

  constructor() {
    console.log('【KimiAdapter】KimiAdapter constructor called')
    super(KimiConfig)
    this.kimiAnswerController = new KimiAnswerController()
    this.kimiAnswerController.init(this);    
    console.log('【KimiAdapter】KimiAdapter initialized with config:', KimiConfig)
  }

  /**
   * 销毁适配器，清理所有资源
   */
  destroy(): void {
    try {
      console.log('【KimiAdapter】KimiAdapter destroy called');
      // 调用父类销毁方法
      super.destroy();
      
      // 清理 KimiAnswerController
      if (this.kimiAnswerController) {

        
        this.kimiAnswerController.destroy();
        this.kimiAnswerController = null;
        console.log('【KimiAdapter】KimiAnswerController 清理完成');
      }
      
      console.log('【KimiAdapter】KimiAdapter destroy 完成');
      
    } catch (error) {
      console.error('【KimiAdapter】KimiAdapter destroy 失败:', error);
    }
  }

}
