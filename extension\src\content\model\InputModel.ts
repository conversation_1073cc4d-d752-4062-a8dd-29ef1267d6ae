import { Singleton } from '@/common/base';

/**
 * 输入模型
 * 继承Singleton基类，自动拥有单例能力
 */
export class InputModel extends Singleton<InputModel> {

  private currPromptUid: string = '';
  private currPrompt: string = '';
  private instanceId: string;

  constructor() {
    super();
    this.instanceId = Math.random().toString(36).substring(2, 8);
    console.log(`【InputModel】Created instance: ${this.instanceId}`);
  }

  public generatePromptUid(): void {
    const timestamp = Date.now();
    const randomStr = Math.random().toString(36).substring(2, 8);
    this.currPromptUid = `${timestamp}-${randomStr}`;
    console.log('【EchoSync】Generated new prompt uid:', this.currPromptUid);
  }

  public getCurrPromptUid(): string {
    if (this.currPromptUid == '') {
      this.generatePromptUid();
    }
    return this.currPromptUid;
  }

  /**
   * 设置提示词UID
   * @param uid 提示词UID
   */
  public setPromptUid(uid: string): void {
    if (!uid || uid.trim().length === 0) {
      throw new Error('Prompt UID cannot be empty');
    }
    this.currPromptUid = uid;
  }

  /**
   * 主动设置uid（保持向后兼容）
   * @param uid
   * @deprecated 使用setPromptUid代替
   */
  public setCurrChatUid(uid: string): void {
    this.setPromptUid(uid);
  }

  /**
   * 获取当前Chat UID（保持向后兼容）
   * @deprecated 使用getCurrPromptUid代替
   */
  public getCurrChatUid(): string {
    return this.getCurrPromptUid();
  }

  /**
   * 生成Chat UID（保持向后兼容）
   * @deprecated 使用generatePromptUid代替
   */
  public generateChatUid(): void {
    this.generatePromptUid();
  }

  public setCurrPrompt(currentValue: string) {
    this.currPrompt = currentValue;
  }

  public getCurrPrompt(): string {
    return this.currPrompt;
  }

  /**
   * 清理数据
   */
  public clear(): void {
    this.currPromptUid = '';
  }

  /**
   * 可选：实现destroy方法用于清理
   */
  public destroy(): void {
    this.clear();
  }
}

