import { Singleton } from '@/common/base';

/**
 * 输入模型
 * 继承Singleton基类，自动拥有单例能力
 */
export class InputModel extends Singleton<InputModel> {

  private currChatUid: string = '';
  private currPrompt: string = '';
  private instanceId: string;

  constructor() {
    super();
    this.instanceId = Math.random().toString(36).substring(2, 8);
    console.log(`【InputModel】Created instance: ${this.instanceId}`);
  }

  public generateChatUid(): void {
    const timestamp = Date.now();
    const randomStr = Math.random().toString(36).substring(2, 8);
    this.currChatUid = `${timestamp}-${randomStr}`;
    console.log('【EchoSync】Generated new chat uid:', this.currChatUid);
  }

  public getCurrChatUid(): string {
    if (this.currChatUid == '') {
      this.generateChatUid();
    }
    return this.currChatUid;
  }

  /**
   * 设置聊天UID
   * @param uid 聊天UID
   */
  public setChatUid(uid: string): void {
    if (!uid || uid.trim().length === 0) {
      throw new Error('Chat UID cannot be empty');
    }
    this.currChatUid = uid;
  }

  /**
   * 主动设置uid（保持向后兼容）
   * @param uid
   * @deprecated 使用setChatUid代替
   */
  public setCurrChatUid(uid: string): void {
    this.setChatUid(uid);
  }

  public setCurrPrompt(currentValue: string) {
    this.currPrompt = currentValue;
  }

  public getCurrPrompt(): string {
    return this.currPrompt;
  }

  /**
   * 清理数据
   */
  public clear(): void {
    this.currChatUid = '';
  }

  /**
   * 可选：实现destroy方法用于清理
   */
  public destroy(): void {
    this.clear();
  }
}

