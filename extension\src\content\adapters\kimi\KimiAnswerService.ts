import { ErrorType, ErrorHandler } from "../../types/ErrorTypes";
import { KimiSelectorService } from "./KimiSelectorService";
import { KimiClipboardService } from "./KimiClipboardService";
import { KimiAnswerModel } from "../../model/KimiAnswerModel";
import SelectorManager from "../../configs/SelectorManager";
import { DOMUtils } from "../../utils/DOMUtils";

/**
 * Kimi答案捕获服务
 * 专门处理答案监听、检测和内容提取
 */
export class KimiAnswerService {
    private answerObserver: MutationObserver | null = null;
    private readonly OBSERVER_CONFIG = { childList: true, subtree: true };
    private readonly ANSWER_TIMEOUT = 30000; // 30秒答案超时

    /**
     * 在指定的聊天列表节点上启动监听
     * @param chatListElement 聊天列表节点
     */
    public async startListeningOnChatList(chatListElement: Element): Promise<void> {
        try {
            console.info('[KimiAnswerService] 在指定聊天列表节点上启动监听');
            
            // 先停止之前的监听器
            this.stopAnswerListener();

            // 获取现有的问题和答案
            await this.captureExistingQAPairs(chatListElement);
            
            // 在指定节点上创建新的监听器
            this.answerObserver = new MutationObserver((mutations) => {
                this.handleMutations(mutations);
            });
            
            // 在聊天列表节点上启动监听
            this.answerObserver.observe(chatListElement, this.OBSERVER_CONFIG);
            console.info('[KimiAnswerService] 聊天列表监听器设置完成');
            
        } catch (error) {
            ErrorHandler.handle(ErrorType.OBSERVER_INIT_FAILED, error, { type: 'chatList' });
        }
    }

    /**
     * 捕获现有的问答对
     * @param chatListElement 聊天列表节点
     */
    private async captureExistingQAPairs(chatListElement: Element): Promise<void> {
        try {
            console.info('[KimiAnswerService] 开始捕获现有问答对');
            const selectors = SelectorManager.getSelector();

            // 获取所有已存在的问题节点
            const PromptElements = DOMUtils.findElementAllInContainer(chatListElement, selectors.promptItem);
            console.info(`[KimiAnswerService] 找到 ${PromptElements.length} 个问题节点`);
            // 逐个处理问题节点
            for (let i = 0; i < PromptElements.length; i++) {
                const PromptElement = PromptElements[i];
                await this.processExistingPrompt(PromptElement);
            }

            // 获取所有已存在的答案节点
            const answerElements = DOMUtils.findElementAllInContainer(chatListElement, selectors.answerItem);
            console.info(`[KimiAnswerService] 找到 ${answerElements.length} 个答案节点`);
            // 逐个处理已存在的答案节点
            for (let i = 0; i < answerElements.length; i++) {
                const answerElement = answerElements[i];
                await this.processExistingAnswer(answerElement);
            }
            
            console.info(`[KimiAnswerService] 现有问答对捕获完成，处理了 ${answerElements.length} 个答案`);
        } catch (error) {
            console.error('[KimiAnswerService] 捕获现有问答对失败', error);
        }
    }

    /**
     * 处理已存在的问题节点
     * @param PromptElement 问题节点
     */ 
    private async processExistingPrompt(PromptElement: Element): Promise<void> {
            const selectors = SelectorManager.getSelector();
            // 检查是否有完成状态标识
            const contentNode = DOMUtils.findElementInContainer(PromptElement, selectors.promptContent);
            // 获得文本
            if(contentNode){
                contentNode.textContent = contentNode.textContent?.trim();
                KimiAnswerModel.getInstance().addPrompt(contentNode.textContent);
            }
    }

    /**
     * 处理已存在的答案节点
     * @param answerElement 答案节点
     */
    private async processExistingAnswer(answerElement: Element): Promise<void> {
        try {
            const selectors = SelectorManager.getSelector();
            // 检查是否有完成状态标识
            const completionNode = DOMUtils.findElementInContainer(answerElement, selectors.answerCompletion);
            if (!completionNode) {
                console.warn('[KimiAnswerService] 答案节点未完成，跳过处理');
                return;
            }

            // 查找复制按钮
            const copyButton = KimiSelectorService.findCompletionButton(completionNode);
            if (!copyButton) {
                console.warn('[KimiAnswerService] 未找到复制按钮，跳过处理');
                return;
            }
            
            // 使用剪贴板服务获取内容
            const answerContent = await KimiClipboardService.simulateClickAndGetContent(answerElement, copyButton);
            
            if (answerContent && answerContent.trim()) {                
                // 存入 KimiAnswerModel
                const answerModel = KimiAnswerModel.getInstance();
                // await answerModel.savePromptAnswerPair(Prompt, answerContent);
                answerModel.addAnswer(answerContent);
            } else {
                console.warn('[KimiAnswerService] 现有答案内容为空');
            }
            
        } catch (error) {
            console.error('[KimiAnswerService] 处理现有答案失败', error);
        }
    }

    /**
     * 处理 DOM 变化
     * @param mutations DOM 变化列表
     */
    private handleMutations(mutations: MutationRecord[]): void {
        try {
            mutations.forEach((mutation) => {
                // console.log("[KimiAnswerService] DOM 节点变化", mutation)
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    mutation.addedNodes.forEach((node) => {
                        if (node instanceof Element) {
                            this.handleNewNode(node);
                        }
                    });
                }
            });
        } catch (error) {
            ErrorHandler.handle(ErrorType.ANSWER_CAPTURE_FAILED, error);
        }
    }

    /**
     * 处理新添加的节点
     * @param node 新节点
     */
    private handleNewNode(node: Element): void {
        const selectors = SelectorManager.getSelector();
        if (!selectors?.answerItem) {
            console.warn('[KimiAnswerService] 未找到答案项选择器配置');
            return;
        }

        // 收集所有需要处理的答案元素（避免重复处理）
        const answerElements = new Set<Element>();

        // 检查节点本身是否为AI答案节点
        const answerSelectors = selectors.answerItem;
        if (answerSelectors.some(sel => node.matches(sel))) {
            console.info('[KimiAnswerService] 检测到新的AI答案元素');
            answerElements.add(node);
        }

        // 查找子节点中的答案元素
        const childAnswerElements = DOMUtils.findElementAllInContainer(node, selectors.answerItem);
        if (childAnswerElements && childAnswerElements.length > 0) {
            childAnswerElements.forEach(element => {
                console.info('[KimiAnswerService] 在子节点中检测到AI答案元素');
                answerElements.add(element);
            });
        }

        // 统一处理所有答案元素
        answerElements.forEach(element => {
            this.handleNewAnswerElement(element);
        });
    }

    /**
     * 处理新的答案元素
     * 注意：调用此方法前已确认 element 是答案元素
     */
    private handleNewAnswerElement(element: Element): void {
        console.info('[KimiAnswerService] 开始处理AI答案元素');

        // 等待答案完成状态
        this.waitForAnswerCompletion(element);
    }

    /**
     * 等待答案完成并提取内容
     */
    private waitForAnswerCompletion(answerElement: Element): void {
        const startTime = Date.now();

        const checkCompletion = () => {
            // 检查是否超时
            if (Date.now() - startTime > this.ANSWER_TIMEOUT) {
                console.warn('[KimiAnswerService] 答案完成检测超时');
                return;
            }

            // 查找完成状态标识节点
            const selectors = SelectorManager.getSelector();
            if (!selectors?.answerCompletion) {
                console.warn('[KimiAnswerService] 未找到答案完成选择器配置');
                return;
            }

            const completionNode = DOMUtils.findElementInContainer(answerElement, selectors.answerCompletion);
            if (completionNode) {
                console.info('[KimiAnswerService] 检测到答案完成标识，开始提取内容');
                this.extractAnswerContent(answerElement, completionNode);
            } else {
                // 继续等待
                setTimeout(checkCompletion, 500);
            }
        };

        checkCompletion();
    }

    /**
     * 提取答案内容
     */
    private async extractAnswerContent(answerElement: Element, completionNode: Element): Promise<void> {
        try {
            // 查找对应的问题
            // const Prompt = this.findCorrespondingPrompt(answerElement);
            // if (!Prompt) {
            //     console.warn('[KimiAnswerService] 未找到对应的问题');
            //     return;
            // }
            // 查找复制按钮
            const copyButton = KimiSelectorService.findCompletionButton(completionNode);
            if (!copyButton) {
                console.warn('[KimiAnswerService] 未找到复制按钮');
                return;
            }

            // 使用剪贴板服务获取内容
            const answerContent = await KimiClipboardService.simulateClickAndGetContent(answerElement, copyButton);

            if (answerContent && answerContent.trim()) {
                // 直接存入 KimiAnswerModel
                const answerModel = KimiAnswerModel.getInstance();
                // await answerModel.savePromptAnswerPair(Prompt, answerContent);
                answerModel.addAnswer(answerContent);
                
                console.info('[KimiAnswerService] 问答对已保存到数据模型');
            } else {
                console.warn('[KimiAnswerService] 提取的答案内容为空');
            }
        } catch (error) {
            console.error('[KimiAnswerService] 答案内容提取失败', error);
        }
    }

    // /**
    //  * 查找对应的问题
    //  */
    // private findCorrespondingPrompt(answerElement: Element): string | null {
    //     try {
    //         const selectors = SelectorManager.getSelector();
    //         if (!selectors?.promptItem) {
    //             console.warn('[KimiAnswerService] 未找到问题项选择器配置');
    //             return null;
    //         }

    //         // 向前查找最近的用户问题元素
    //         let previousElement = answerElement.previousElementSibling;
    //         const PromptSelector = selectors.promptItem[0];

    //         while (previousElement) {
    //             if (previousElement.matches(PromptSelector)) {
    //                 // 提取问题文本
    //                 const PromptText = this.extractPromptText(previousElement);
    //                 if (PromptText) {
    //                     return PromptText;
    //                 }
    //             }
    //             previousElement = previousElement.previousElementSibling;
    //         }

    //         return null;
    //     } catch (error) {
    //         console.error('[KimiAnswerService] 查找对应问题失败', error);
    //         return null;
    //     }
    // }

    /**
     * 提取问题文本
     */
    private extractPromptText(PromptElement: Element): string | null {
        try {
            // 查找问题内容容器
            const contentElement = PromptElement.querySelector('.markdown, .message-content, .content');
            if (contentElement) {
                return contentElement.textContent?.trim() || null;
            }

            // 降级：直接从元素提取文本
            return PromptElement.textContent?.trim() || null;
        } catch (error) {
            console.error('[KimiAnswerService] 提取问题文本失败', error);
            return null;
        }
    }

    /**
     * 停止答案监听
     */
    public stopAnswerListener(): void {
        if (this.answerObserver) {
            this.answerObserver.disconnect();
            this.answerObserver = null;
            console.info('[KimiAnswerService] 答案监听器已停止');
        }
    }

    /**
     * 销毁服务
     */
    public destroy(): void {
        this.stopAnswerListener();
        
        console.info('[KimiAnswerService] 答案服务已销毁');
    }
}