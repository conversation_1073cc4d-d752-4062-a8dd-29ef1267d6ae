import { ErrorType, ErrorHandler } from "../../types/ErrorTypes";
import { KimiSelectorService } from "./KimiSelectorService";
import { KimiClipboardService } from "./KimiClipboardService";
import { KimiAnswerModel } from "../../model/KimiAnswerModel";
import SelectorManager from "../../configs/SelectorManager";
import { DOMUtils } from "../../utils/DOMUtils";

/**
 * Kimi答案捕获服务
 * 专门处理答案监听、检测和内容提取
 */
export class KimiAnswerService {
    private answerObserver: MutationObserver | null = null;
    private readonly OBSERVER_CONFIG = { childList: true, subtree: true };
    private readonly ANSWER_TIMEOUT = 30000; // 30秒答案超时

    /**
     * 在指定的聊天列表节点上启动监听
     * @param chatListElement 聊天列表节点
     */
    public async startListeningOnChatList(chatListElement: Element): Promise<void> {
        try {
            console.info('[KimiAnswerService] 在指定聊天列表节点上启动监听');
            
            // 先停止之前的监听器
            this.stopAnswerListener();

            // 获取现有的问题和答案
            await this.captureExistingQAPairs(chatListElement);
            
            // 在指定节点上创建新的监听器
            this.answerObserver = new MutationObserver((mutations) => {
                this.handleMutations(mutations);
            });
            
            // 在聊天列表节点上启动监听
            this.answerObserver.observe(chatListElement, this.OBSERVER_CONFIG);
            console.info('[KimiAnswerService] 聊天列表监听器设置完成');
            
        } catch (error) {
            ErrorHandler.handle(ErrorType.OBSERVER_INIT_FAILED, error, { type: 'chatList' });
        }
    }

    /**
     * 捕获现有的问答对
     * @param chatListElement 聊天列表节点
     */
    private async captureExistingQAPairs(chatListElement: Element): Promise<void> {
        try {
            console.info('[KimiAnswerService] 开始捕获现有问答对');

            const selectors = SelectorManager.getSelector();
            if (!selectors) {
                console.warn('[KimiAnswerService] 未找到选择器配置');
                return;
            }

            // 获取所有已存在的问题节点
            const questionElements = DOMUtils.findElementAllInContainer(chatListElement, selectors.questionItem);
            console.info(`[KimiAnswerService] 找到 ${questionElements.length} 个问题节点`);

            // 获取所有已存在的答案节点
            const answerElements = DOMUtils.findElementAllInContainer(chatListElement, selectors.answerItem);
            console.info(`[KimiAnswerService] 找到 ${answerElements.length} 个答案节点`);

            // 逐个处理已存在的答案节点
            for (let i = 0; i < answerElements.length; i++) {
                const answerElement = answerElements[i];
                await this.processExistingAnswer(answerElement);
            }
            
            console.info(`[KimiAnswerService] 现有问答对捕获完成，处理了 ${answerElements.length} 个答案`);
            
        } catch (error) {
            console.error('[KimiAnswerService] 捕获现有问答对失败', error);
        }
    }

    /**
     * 处理已存在的答案节点
     * @param answerElement 答案节点
     */
    private async processExistingAnswer(answerElement: Element): Promise<void> {
        try {
            const selectors = SelectorManager.getSelector();
            if (!selectors?.answerCompletion) {
                console.warn('[KimiAnswerService] 未找到答案完成选择器配置');
                return;
            }

            // 检查是否有完成状态标识
            const completionNode = DOMUtils.findElementInContainer(answerElement, selectors.answerCompletion);
            if (!completionNode) {
                console.warn('[KimiAnswerService] 答案节点未完成，跳过处理');
                return;
            }
            
            // 查找对应的问题
            const question = this.findCorrespondingQuestion(answerElement);
            if (!question) {
                console.warn('[KimiAnswerService] 未找到对应的问题，跳过处理');
                return;
            }
            
            // 查找复制按钮
            const copyButton = KimiSelectorService.findCompletionButton(completionNode);
            if (!copyButton) {
                console.warn('[KimiAnswerService] 未找到复制按钮，跳过处理');
                return;
            }
            
            // 使用剪贴板服务获取内容
            const answerContent = await KimiClipboardService.simulateClickAndGetContent(copyButton);
            
            if (answerContent && answerContent.trim()) {
                console.info('[KimiAnswerService] 成功提取现有答案内容', {
                    questionLength: question.length,
                    answerLength: answerContent.length
                });
                
                // 存入 KimiAnswerModel
                const answerModel = KimiAnswerModel.getInstance();
                await answerModel.saveQuestionAnswerPair(question, answerContent);
                
                console.info('[KimiAnswerService] 现有问答对已保存到数据模型');
            } else {
                console.warn('[KimiAnswerService] 现有答案内容为空');
            }
            
        } catch (error) {
            console.error('[KimiAnswerService] 处理现有答案失败', error);
        }
    }

    /**
     * 处理 DOM 变化
     * @param mutations DOM 变化列表
     */
    private handleMutations(mutations: MutationRecord[]): void {
        try {
            mutations.forEach((mutation) => {
                console.log("[KimiAnswerService] DOM 节点变化", mutation)
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    mutation.addedNodes.forEach((node) => {
                        if (node instanceof Element) {
                            this.handleNewNode(node);
                        }
                    });
                }
            });
        } catch (error) {
            ErrorHandler.handle(ErrorType.ANSWER_CAPTURE_FAILED, error);
        }
    }

    /**
     * 处理新添加的节点
     * @param node 新节点
     */
    private handleNewNode(node: Element): void {
        const selectors = SelectorManager.getSelector();
        if (!selectors?.answerItem) {
            console.warn('[KimiAnswerService] 未找到答案项选择器配置');
            return;
        }

        // 检查是否为AI答案节点
        const answerSelector = selectors.answerItem[0];
        if (node.matches(answerSelector)) {
            console.info('[KimiAnswerService] 检测到新的AI答案元素');
            this.handleNewAnswerElement(node);
        }

        // 递归检查子节点
        const answerElements = node.querySelectorAll(answerSelector);
        answerElements.forEach(element => {
            console.info('[KimiAnswerService] 在子节点中检测到AI答案元素');
            this.handleNewAnswerElement(element);
        });
    }

    /**
     * 处理新的答案元素
     */
    private handleNewAnswerElement(element: Element): void {
        const selectors = SelectorManager.getSelector();
        if (!selectors?.answerItem) {
            console.warn('[KimiAnswerService] 未找到答案项选择器配置');
            return;
        }

        // 检查是否为AI答案节点
        const answerSelector = selectors.answerItem[0];
        if (element.matches(answerSelector)) {
            console.info('[KimiAnswerService] 检测到新的AI答案元素');

            // 等待答案完成状态
            this.waitForAnswerCompletion(element);
        }
    }

    /**
     * 等待答案完成并提取内容
     */
    private waitForAnswerCompletion(answerElement: Element): void {
        const startTime = Date.now();

        const checkCompletion = () => {
            // 检查是否超时
            if (Date.now() - startTime > this.ANSWER_TIMEOUT) {
                console.warn('[KimiAnswerService] 答案完成检测超时');
                return;
            }

            // 查找完成状态标识节点
            const selectors = SelectorManager.getSelector();
            if (!selectors?.answerCompletion) {
                console.warn('[KimiAnswerService] 未找到答案完成选择器配置');
                return;
            }

            const completionNode = DOMUtils.findElementInContainer(answerElement, selectors.answerCompletion);
            if (completionNode) {
                console.info('[KimiAnswerService] 检测到答案完成标识，开始提取内容');
                this.extractAnswerContent(answerElement, completionNode);
            } else {
                // 继续等待
                setTimeout(checkCompletion, 500);
            }
        };

        checkCompletion();
    }

    /**
     * 提取答案内容
     */
    private async extractAnswerContent(answerElement: Element, completionNode: Element): Promise<void> {
        try {
            // 查找对应的问题
            const question = this.findCorrespondingQuestion(answerElement);
            if (!question) {
                console.warn('[KimiAnswerService] 未找到对应的问题');
                return;
            }

            // 查找复制按钮
            const copyButton = KimiSelectorService.findCompletionButton(completionNode);
            if (!copyButton) {
                console.warn('[KimiAnswerService] 未找到复制按钮');
                return;
            }

            // 使用剪贴板服务获取内容
            const answerContent = await KimiClipboardService.simulateClickAndGetContent(copyButton);

            if (answerContent && answerContent.trim()) {
                console.info('[KimiAnswerService] 成功提取答案内容', {
                    questionLength: question.length,
                    answerLength: answerContent.length
                });

                // 直接存入 KimiAnswerModel
                const answerModel = KimiAnswerModel.getInstance();
                await answerModel.saveQuestionAnswerPair(question, answerContent);
                
                console.info('[KimiAnswerService] 问答对已保存到数据模型');
            } else {
                console.warn('[KimiAnswerService] 提取的答案内容为空');
            }
        } catch (error) {
            console.error('[KimiAnswerService] 答案内容提取失败', error);
        }
    }

    /**
     * 查找对应的问题
     */
    private findCorrespondingQuestion(answerElement: Element): string | null {
        try {
            const selectors = SelectorManager.getSelector();
            if (!selectors?.questionItem) {
                console.warn('[KimiAnswerService] 未找到问题项选择器配置');
                return null;
            }

            // 向前查找最近的用户问题元素
            let previousElement = answerElement.previousElementSibling;
            const questionSelector = selectors.questionItem[0];

            while (previousElement) {
                if (previousElement.matches(questionSelector)) {
                    // 提取问题文本
                    const questionText = this.extractQuestionText(previousElement);
                    if (questionText) {
                        return questionText;
                    }
                }
                previousElement = previousElement.previousElementSibling;
            }

            return null;
        } catch (error) {
            console.error('[KimiAnswerService] 查找对应问题失败', error);
            return null;
        }
    }

    /**
     * 提取问题文本
     */
    private extractQuestionText(questionElement: Element): string | null {
        try {
            // 查找问题内容容器
            const contentElement = questionElement.querySelector('.markdown, .message-content, .content');
            if (contentElement) {
                return contentElement.textContent?.trim() || null;
            }

            // 降级：直接从元素提取文本
            return questionElement.textContent?.trim() || null;
        } catch (error) {
            console.error('[KimiAnswerService] 提取问题文本失败', error);
            return null;
        }
    }

    /**
     * 停止答案监听
     */
    public stopAnswerListener(): void {
        if (this.answerObserver) {
            this.answerObserver.disconnect();
            this.answerObserver = null;
            console.info('[KimiAnswerService] 答案监听器已停止');
        }
    }

    /**
     * 销毁服务
     */
    public destroy(): void {
        this.stopAnswerListener();
        console.info('[KimiAnswerService] 答案服务已销毁');
    }

    /**
     * 获取服务状态
     */
    public getServiceStatus(): { hasAnswerObserver: boolean } {
        return {
            hasAnswerObserver: !!this.answerObserver
        };
    }
}