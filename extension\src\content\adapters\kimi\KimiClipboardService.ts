import { ErrorType, ErrorHandler } from "../../types/ErrorTypes";
import { KimiSelectorService } from "./KimiSelectorService";
import TurndownService from 'turndown';
import SelectorManager from "../../configs/SelectorManager";
import { DOMUtils } from "../../utils/DOMUtils";

/**
 * Kimi剪贴板管理服务
 * 负责处理剪贴板的读取和写入操作
 */
export class KimiClipboardService {
    
    /**
     * 模拟点击并获取剪贴板内容
     * 基于segment-assistant-actions-content节点的新方法
     * @param targetElement 目标节点（segment-assistant-actions-content或其子节点）
     * @returns 剪贴板内容
     */
    public static async simulateClickAndGetContent(answerElement: Element, copyButton: Element): Promise<string> {
        try {
            console.info('[KimiClipboardService] 开始新的内容提取流程', copyButton);
            
            // 模拟点击复制按钮
            (copyButton as HTMLElement).click();
            console.info('[KimiClipboardService] 执行复制按钮点击');
            
            // 等待剪贴板操作完成
            await this.delay(300); // 增加等待时间确保复制完成
            
            // 获取剪贴板内容
            const clipboardContent = await this.getClipboardContent(answerElement);
            console.info('[KimiClipboardService] 剪贴板内容获取成功', clipboardContent.slice(0, 10));
            
            return clipboardContent;
        } catch (error) {
            console.error('[KimiClipboardService] 模拟点击和内容获取失败', error);
            return '';
        }
    }
    
   
    /**
     * 获取剪贴板内容使用现代API
     * @returns 剪贴板内容
     */
    private static async getClipboardContent(answerElement: Element): Promise<string> {
        try {
            // 检查浏览器支持
            if (!navigator.clipboard || !navigator.clipboard.readText) {
                console.warn('[KimiClipboardService] 浏览器不支持 Clipboard API，使用降级方法');
                return await this.getFallbackClipboardContent(answerElement);
            }
            
            // 检查权限（不强制要求，先尝试读取）
            try {
                const content = await navigator.clipboard.readText();
                console.info('[KimiClipboardService] 成功读取剪贴板内容');
                return content || '';
            } catch (permissionError) {
                console.warn('[KimiClipboardService] 剪贴板权限被拒绝或操作失败，尝试降级方法');
                return await this.getFallbackClipboardContent(answerElement);
            }
            
        } catch (error) {
            ErrorHandler.handle(ErrorType.CLIPBOARD_ACCESS_FAILED, error);
            console.warn('[KimiClipboardService] 主方法失败，尝试降级方法');
            return await this.getFallbackClipboardContent(answerElement);
        }
    }

    /**
     * 降级的剪贴板内容获取方法
     * 从 answerElement 中通过选择器获取 markdown 内容
     * @param answerElement 答案元素
     * @returns 剪贴板内容
     */
    private static async getFallbackClipboardContent(answerElement: Element): Promise<string> {
        try {
            console.warn('[KimiClipboardService] 使用降级剪贴板读取方法 - 从DOM提取markdown内容');
            
            // 1. 从answerElement节点，通过选择器获得markdown根节点
            const markdownSelectors = SelectorManager.getSelector()?.markdown;
            if (!markdownSelectors || markdownSelectors.length === 0) {
                console.warn('[KimiClipboardService] 未找到markdown选择器配置');
                return '';
            }

            // 使用 DOMUtils.findElementInContainer 查找 markdown 节点
            const markdownNode = DOMUtils.findElementInContainer(answerElement, markdownSelectors);
            if (!markdownNode) {
                console.warn('[KimiClipboardService] 未找到markdown节点，返回空内容');
                return '';
            }

            // 2. 获取 HTML 内容
            const htmlContent = markdownNode.innerHTML;
            if (!htmlContent || htmlContent.trim().length === 0) {
                console.warn('[KimiClipboardService] markdown节点HTML内容为空');
                return '';
            }

            // 3. 使用 turndown 库把 HTML → Markdown
            const turndownService = new TurndownService({
                headingStyle: 'atx',
                codeBlockStyle: 'fenced',
                emDelimiter: '*'
            });
            
            const markdownContent = turndownService.turndown(htmlContent);
            
            console.info('[KimiClipboardService] 成功从DOM提取并转换markdown内容', markdownContent.slice(0, 100));
            
            // 4. 返回markdown内容
            return markdownContent || '';
            
        } catch (error) {
            console.error('[KimiClipboardService] 降级剪贴板读取也失败', error);
            return '';
        }
    }

    /**
     * 延迟执行工具方法
     * @param ms 延迟毫秒数
     */
    private static delay(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}