import { ErrorType, ErrorHandler } from "../../types/ErrorTypes";

/**
 * Kimi剪贴板管理服务
 * 负责处理剪贴板的读取和写入操作
 */
export class KimiClipboardService {
    
    /**
     * 模拟点击并获取剪贴板内容
     * 基于segment-assistant-actions-content节点的新方法
     * @param targetElement 目标节点（segment-assistant-actions-content或其子节点）
     * @returns 剪贴板内容
     */
    public static async simulateClickAndGetContent(targetElement: Element): Promise<string> {
        try {
            console.info('[KimiClipboardService] 开始新的内容提取流程');
            
            // 查找实际的复制按钮
            const copyButton = this.findCopyButton(targetElement);
            if (!copyButton) {
                console.warn('[KimiClipboardService] 未找到复制按钮');
                return '';
            }
            
            // 模拟点击复制按钮
            (copyButton as HTMLElement).click();
            console.info('[KimiClipboardService] 执行复制按钮点击');
            
            // 等待剪贴板操作完成
            await this.delay(300); // 增加等待时间确保复制完成
            
            // 获取剪贴板内容
            const clipboardContent = await this.getClipboardContent();
            console.info('[KimiClipboardService] 剪贴板内容获取成功', {
                contentLength: clipboardContent.length,
                preview: clipboardContent.substring(0, 100) + (clipboardContent.length > 100 ? '...' : '')
            });
            
            return clipboardContent;
        } catch (error) {
            console.error('[KimiClipboardService] 模拟点击和内容获取失败', error);
            return '';
        }
    }
    
    /**
     * 查找复制按钮
     * 支持多种选择器策略
     */
    private static findCopyButton(container: Element): Element | null {
        // 优先级选择器列表（基于新DOM结构）
        const selectors = [
            '.simple-button.size-small',  // 主要目标
            '.segment-assistant-actions-content .simple-button',
            '.copy-button',
            '.action-button',
            '[data-testid="copy-button"]',
            '[aria-label*="复制"]',
            '[aria-label*="Copy"]',
            'button[title*="复制"]',
            'button[title*="Copy"]',
            '.assistant-actions .copy-button',
            '.response-actions .action-button'
        ];
        
        // 在容器内查找
        for (const selector of selectors) {
            const button = container.querySelector(selector);
            if (button) {
                console.info(`[KimiClipboardService] 找到复制按钮: ${selector}`);
                return button;
            }
        }
        
        // 在父容器中查找
        if (container.parentElement) {
            for (const selector of selectors) {
                const button = container.parentElement.querySelector(selector);
                if (button) {
                    console.info(`[KimiClipboardService] 在父容器中找到复制按钮: ${selector}`);
                    return button;
                }
            }
        }
        
        console.warn('[KimiClipboardService] 未找到任何复制按钮');
        return null;
    }

    /**
     * 获取剪贴板内容使用现代API
     * @returns 剪贴板内容
     */
    private static async getClipboardContent(): Promise<string> {
        try {
            // 检查浏览器支持
            if (!navigator.clipboard || !navigator.clipboard.readText) {
                console.warn('[KimiClipboardService] 浏览器不支持 Clipboard API，使用降级方法');
                return await this.getFallbackClipboardContent();
            }
            
            // 检查权限（不强制要求，先尝试读取）
            try {
                const content = await navigator.clipboard.readText();
                console.info('[KimiClipboardService] 成功读取剪贴板内容');
                return content || '';
            } catch (permissionError) {
                console.warn('[KimiClipboardService] 剪贴板权限被拒绝或操作失败，尝试降级方法');
                return await this.getFallbackClipboardContent();
            }
            
        } catch (error) {
            ErrorHandler.handle(ErrorType.CLIPBOARD_ACCESS_FAILED, error);
            console.warn('[KimiClipboardService] 主方法失败，尝试降级方法');
            return await this.getFallbackClipboardContent();
        }
    }

    /**
     * 降级的剪贴板内容获取方法
     * @returns 剪贴板内容
     */
    private static async getFallbackClipboardContent(): Promise<string> {
        try {
            console.warn('[KimiClipboardService] 使用降级剪贴板读取方法');
            
            // 创建隐藏的文本域
            const textArea = document.createElement('textarea');
            textArea.style.position = 'fixed';
            textArea.style.top = '-9999px';
            textArea.style.left = '-9999px';
            textArea.style.opacity = '0';
            document.body.appendChild(textArea);
            
            try {
                textArea.focus();
                textArea.select();
                
                // 尝试粘贴
                const success = document.execCommand('paste');
                if (success) {
                    const content = textArea.value;
                    console.info('[KimiClipboardService] 降级方法成功获取内容', content);
                    return content;
                } else {
                    throw new Error('执行paste命令失败');
                }
            } finally {
                document.body.removeChild(textArea);
            }
        } catch (error) {
            console.error('[KimiClipboardService] 降级剪贴板读取也失败', error);
            return '';
        }
    }

    /**
     * 延迟执行工具方法
     * @param ms 延迟毫秒数
     */
    private static delay(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}