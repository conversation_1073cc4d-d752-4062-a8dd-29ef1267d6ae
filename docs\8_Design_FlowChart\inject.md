# 注入小球与输入框和数据库交互流程图

本文档详细描述了注入浮动小球组件，并实现与输入框和数据库交互的完整流程。

## 一、整体注入交互流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant FB as FloatingBubble
    participant FI as FloatingBubbleInject
    participant BA as BaseAIAdapter
    participant IC as InputCapture
    participant MS as MessagingService
    participant MH as MessageHandler
    participant CHS as ChatHistoryService

    BA->>FI: new FloatingBubbleInject(adapter)
    FI->>FB: new FloatingBubble()
    FI->>FI: inject()
    FI->>FB: document.body.appendChild(bubble)

    U->>FB: 点击浮动小球
    FB->>FI: handleClick 事件
    FI->>FI: 派发 FLOATING_BUBBLE_CLICKED 事件
    FI->>FI: 派发 SHOW_STORED_PROMPTS 事件

    Note over FI: 实际的数据保存通过其他事件触发
    IC->>IC: handleSendEvent()
    IC->>IC: 派发 echosync:prompt-send 事件
    IC->>MS: MessagingService.sendToBackground
    MS->>MH: MessageHandler.handleMessage
    MH->>CHS: chatHistoryService.createWithPrompt
    CHS-->>MH: 返回保存结果
    MH-->>MS: sendResponse
    MS-->>IC: 返回结果
```

## 二、浮动小球注入详细流程

```mermaid
flowchart TD
    A[BaseAIAdapter.initInject] --> B[new FloatingBubbleInject]
    B --> C[new FloatingBubble]
    C --> D[component.render]

    D --> E[创建 DOM 元素]
    E --> F[设置样式和位置]
    F --> G[setupEventListeners]

    G --> H[inject 方法]
    H --> I[document.body.appendChild]

    I --> J[new FLoatingBubbleDrag]
    J --> K[setupBubbleEventListeners]

    K --> L[addEventListener mouseenter/mouseleave]
    L --> M[addEventListener click]
    M --> N[addEventListener contextmenu]

    N --> O[initializeHistoryBubble]
    O --> P[new HistoryBubbleIService]

    P --> Q[setupWindowResizeListener]
    Q --> R[小球注入完成]

    R --> S[监听自定义事件]
    S --> T[INPUT_FOCUSED, PAGE_CHANGED 等]
```

## 三、小球与输入框交互流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant FB as FloatingBubble
    participant FI as FloatingBubbleInject
    participant A as BaseAIAdapter
    participant IC as InputCapture
    participant DOM as Page DOM

    Note over U,DOM: 用户在输入框中输入内容
    U->>DOM: 在输入框中输入文本
    DOM->>IC: 触发 input 事件
    IC->>A: 通知输入内容变化
    A->>FB: 更新小球状态
    FB->>FB: 显示输入提示状态
    
    Note over U,DOM: 用户点击浮动小球
    U->>FB: 点击浮动小球
    FB->>FI: 触发 click 事件
    FI->>A: 调用 handleBubbleClick
    A->>IC: 获取当前输入内容
    IC->>DOM: 读取输入框内容
    DOM-->>IC: 返回输入文本
    IC-->>A: 返回输入内容
    
    A->>A: 验证输入内容
    alt 输入内容有效
        A->>A: 准备保存数据
        A->>FB: 显示保存中状态
        FB->>FB: 更新小球图标为加载状态
    else 输入内容为空
        A->>FB: 显示错误提示
        FB->>FB: 显示错误状态
        FB->>U: 提示用户输入内容
    end
```

## 四、小球与数据库交互流程

```mermaid
flowchart TD
    A[用户点击小球] --> B[handleClick 事件]
    B --> C[检查 isDragging]

    C --> D{是拖拽后点击?}
    D -->|是| E[preventDefault 阻止]
    D -->|否| F[派发 FLOATING_BUBBLE_CLICKED]

    F --> G[派发 SHOW_STORED_PROMPTS]

    Note1[实际数据保存流程]
    H[用户在输入框输入] --> I[InputCapture.handleSendEvent]
    I --> J[getCurrentInput 获取内容]
    J --> K[派发 echosync:prompt-send]

    K --> L[MessagingService.sendToBackground]
    L --> M[MessageHandler.handleMessage]
    M --> N[chatHistoryService.createWithPrompt]

    N --> O[ChatHistoryDao 数据库操作]
    O --> P{保存成功?}
    P -->|是| Q[返回成功结果]
    P -->|否| R[返回错误信息]

    Q --> S[sendResponse 回调]
    R --> S

    S --> T[Content Script 接收响应]
    T --> U[处理保存结果]

    E --> V[流程结束]
    G --> V
    U --> V
```

## 五、小球事件处理流程

```mermaid
stateDiagram-v2
    [*] --> Injected: 注入完成
    Injected --> Idle: 等待交互
    Idle --> Hover: mouseenter
    Hover --> Idle: mouseleave
    Idle --> Clicked: click 事件
    Clicked --> Idle: 处理完成
    Idle --> Dragging: 开始拖拽
    Dragging --> Idle: 拖拽结束

    state Hover {
        [*] --> ShowTimer: 设置定时器
        ShowTimer --> ShowHistory: 500ms 后
        ShowHistory --> [*]: mouseleave
    }

    state Clicked {
        [*] --> CheckDrag: 检查是否拖拽
        CheckDrag --> Prevent: 是拖拽
        CheckDrag --> Dispatch: 非拖拽
        Dispatch --> [*]: 派发事件
        Prevent --> [*]: 阻止默认行为
    }
```

## 六、拖拽功能实现流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant FB as FloatingBubble
    participant FBD as FLoatingBubbleDrag
    participant LS as LocalStorage

    U->>FB: 鼠标按下 (mousedown)
    FB->>FBD: handleMouseDown
    FBD->>FBD: 记录初始位置
    FBD->>FBD: 设置 isDragging = true
    FBD->>FBD: addEventListener mousemove/mouseup

    loop 拖拽过程
        U->>FBD: 鼠标移动 (mousemove)
        FBD->>FBD: 计算新位置
        FBD->>FBD: 检查边界限制
        FBD->>FB: 更新 transform 样式
    end

    U->>FBD: 鼠标释放 (mouseup)
    FBD->>FBD: 设置 isDragging = false
    FBD->>FBD: removeEventListener
    FBD->>LS: 保存位置到 localStorage
    FBD->>FB: 更新最终位置
```

## 七、小球位置记忆流程

```mermaid
flowchart TD
    A[小球初始化] --> B[读取存储的位置]
    B --> C{有存储位置?}
    
    C -->|是| D[验证位置有效性]
    C -->|否| E[使用默认位置]
    
    D --> F{位置在可视区域?}
    F -->|是| G[应用存储位置]
    F -->|否| H[调整到可视区域]
    
    E --> I[计算默认位置]
    I --> J[右下角偏移位置]
    
    G --> K[设置小球位置]
    H --> K
    J --> K
    
    K --> L[监听窗口大小变化]
    L --> M[动态调整位置]
    
    M --> N[用户拖拽小球]
    N --> O[更新位置]
    O --> P[保存新位置到存储]
    P --> Q[位置记忆完成]
```

## 八、错误处理和恢复机制

```mermaid
flowchart TD
    A[小球操作过程中发生错误] --> B{错误类型判断}
    
    B -->|注入失败| C[DOM注入错误]
    B -->|交互失败| D[事件处理错误]
    B -->|保存失败| E[数据保存错误]
    B -->|网络失败| F[通信错误]
    
    C --> C1[检查页面DOM结构]
    C1 --> C2[重试注入到body]
    C2 --> C3{重试成功?}
    C3 -->|是| G[恢复正常功能]
    C3 -->|否| H[禁用小球功能]
    
    D --> D1[重新绑定事件监听]
    D1 --> D2[检查元素有效性]
    D2 --> D3{元素有效?}
    D3 -->|是| G
    D3 -->|否| I[重新查找目标元素]
    
    E --> E1[显示保存失败提示]
    E1 --> E2[提供重试选项]
    E2 --> E3[用户选择重试]
    E3 --> J[重新执行保存]
    
    F --> F1[显示网络错误]
    F1 --> F2[启用离线模式]
    F2 --> F3[缓存数据到本地]
    
    G --> K[错误恢复完成]
    H --> L[降级模式运行]
    I --> M[重新初始化]
    J --> N[重试保存流程]
    F3 --> O[离线数据管理]
    
    M --> K
    N --> K
    L --> K
    O --> K
```

## 九、性能优化策略

### 9.1 渲染优化

```mermaid
flowchart TD
    A[小球渲染优化] --> B[CSS动画优化]
    A --> C[DOM操作优化]
    A --> D[事件处理优化]
    
    B --> B1[使用 transform 代替 position]
    B --> B2[启用硬件加速]
    B --> B3[避免重排重绘]
    
    C --> C1[批量DOM更新]
    C --> C2[使用 DocumentFragment]
    C --> C3[缓存DOM引用]
    
    D --> D1[事件委托]
    D --> D2[防抖处理]
    D --> D3[及时移除监听器]
```

### 9.2 内存管理

```mermaid
flowchart TD
    A[内存管理优化] --> B[组件生命周期管理]
    A --> C[事件监听器清理]
    A --> D[数据缓存管理]
    
    B --> B1[及时销毁无用组件]
    B --> B2[清理DOM引用]
    B --> B3[释放定时器]
    
    C --> C1[页面卸载时清理]
    C --> C2[组件销毁时清理]
    C --> C3[避免内存泄漏]
    
    D --> D1[限制缓存大小]
    D --> D2[定期清理过期数据]
    D --> D3[使用弱引用]
```

## 十、最佳实践

### 10.1 小球设计原则
1. **非侵入性**: 不影响原页面的正常功能
2. **用户友好**: 提供清晰的视觉反馈
3. **性能优先**: 最小化对页面性能的影响
4. **响应式**: 适配不同屏幕尺寸

### 10.2 交互设计
1. **状态清晰**: 不同状态有明确的视觉区分
2. **操作简单**: 一键完成主要功能
3. **反馈及时**: 操作后立即给出反馈
4. **错误友好**: 错误信息清晰易懂

### 10.3 技术实现
1. **模块化**: 功能模块化，便于维护
2. **可配置**: 支持用户自定义设置
3. **兼容性**: 兼容主流浏览器
4. **安全性**: 防止XSS和其他安全问题

### 10.4 实际性能特点
- 小球注入: 同步创建和添加到 DOM
- 拖拽实现: 基于 transform 的硬件加速
- 事件处理: 原生事件监听，最小开销
- 内存管理: 组件销毁时清理所有引用
