import { Singleton } from "@/common/base/Singleton";
import { BaseAIAdapter } from "../adapters/BaseAIAdapter";
import { InputModel } from "../model/InputModel";
import { CreateChatPromptResp, CreateChatHistoryResp } from "@/common/types/content_vo";
import { DatabaseResult } from "@/common/types/comm_vo";
import { chatPromptDatabaseProxy } from "@/common/service/ChatPromptDatabaseProxy";
import { chatHistoryDatabaseProxy } from "@/common/service/ChatHistoryDatabaseProxy";
import { PlatformEntity } from "@/common/types/database_entity";

/**
 * 
 * content模块存档服务
 */
export default class ArchiveService  extends Singleton<ArchiveService>{
  private archivedChatUidSet: Set<string> = new Set();
  private archivedPromptSet:  Set<string> = new Set();
  private inputModel: InputModel  = InputModel.getInstance()
  
  /**
   * 存档提示词
  */
  public async archivePrompt(inputPrompt: string, platformResult: PlatformEntity): Promise<void> {
    if (!inputPrompt || inputPrompt.trim().length === 0) {
      console.warn('【EchoSync】ArchiveButtonInject: Input is empty, cannot archive')
      return
    }

    // 如果不存在提示词，则生成新的prompt_uid,
    if(!this.archivedPromptSet.has(inputPrompt)) {
      this.inputModel.generatePromptUid()
    }
    const promptUid = this.inputModel.getCurrPromptUid()

    try {
      const archiveData = {
        chat_prompt: inputPrompt,
        prompt_uid: promptUid,
        platform_id: platformResult?.id,
        create_time: Date.now()
      }
      console.info('【EchoSync】Archiving prompt:', archiveData)
      const result: DatabaseResult<CreateChatPromptResp> = await chatPromptDatabaseProxy.createChatPrompt(archiveData)

      if (result.success) {
        this.archivedChatUidSet.add(promptUid)
        this.archivedPromptSet.add(inputPrompt)

        console.log('【EchoSync】Prompt archived successfully:', result.data)

      } else {
        console.error('【EchoSync】Archive failed:', result.error)
      }
    } catch (error) {
      console.error('【EchoSync】Archive prompt error:', error)
    }
  }

  public isArchivedPrompt(prompt: string): boolean {
    return this.archivedPromptSet.has(prompt)
  }

  public isArchivedPromptUid(): boolean {
    return this.archivedChatUidSet.has(this.inputModel.getCurrPromptUid())
  }

  /**
   * 保持向后兼容
   * @deprecated 使用isArchivedPromptUid代替
   */
  public isArchivedChatUid(): boolean {
    return this.isArchivedPromptUid()
  }

  /**
   * 存档答案
   */
  public async archiveAnswer(promptUid: string, answer: string, platform: PlatformEntity): Promise<void> {
    if (!answer || answer.trim().length === 0) {
      console.warn('【ArchiveService】Answer is empty, cannot archive')
      return
    }

    if (!promptUid || promptUid.trim().length === 0) {
      console.warn('【ArchiveService】PromptUid is empty, cannot archive answer')
      return
    }

    try {
      const archiveData = {
        prompt_uid: promptUid,
        platform_id: platform?.id,
        chat_answer: answer,
        create_time: Date.now()
      }

      console.info('【ArchiveService】Archiving answer:', archiveData)
      const result: DatabaseResult<CreateChatHistoryResp> = await chatHistoryDatabaseProxy.createChatHistory(archiveData)

      if (result.success) {
        console.log('【ArchiveService】Answer archived successfully:', result.data)
      } else {
        console.error('【ArchiveService】Archive answer failed:', result.error)
      }
    } catch (error) {
      console.error('【ArchiveService】Archive answer error:', error)
    }
  }

  destroy(): void {
    this.archivedChatUidSet.clear()
    this.archivedPromptSet.clear()
    this.inputModel.destroy()
    console.info('【ArchiveService】Destroyed')
  }

}