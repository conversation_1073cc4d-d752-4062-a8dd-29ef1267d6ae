import { SelectorConfig } from "./SelectorManager";

/**
 * Kimi 适配器的选择器配置
 * 整合了所有 Kimi 平台特有的选择器
 */
export const kimiSelector: SelectorConfig  = {
      inputField: [
        '[data-lexical-editor="true"]', // []包裹为属性选择器
        '.chat-input-editor[contenteditable="true"]',
        '.chat-input-editor', //.前缀为类选择器
        'textarea[placeholder*="请输入"]',
        'textarea[placeholder*="输入"]',
        'textarea[placeholder*="message"]',
        '[data-testid="chat-input"]',
        '.chat-input',
        '[contenteditable="true"]'
      ],
      sendButton: [
        '.send-button-container .send-button',  // 匹配所有状态的发送按钮
        '.send-button',                         // 通用发送按钮选择器
        '[data-testid="send-button"]',
        '[aria-label*="发送"]',
        '[aria-label*="Send"]',
        'button[aria-label*="发送"]',
        'button[aria-label*="Send"]',
        '.send-btn',
        '[data-testid*="send"]'
      ],
      

      // Kimi 特有的选择器
      headerContent: [
        '.chat-header-content'
      ],
      headerValue: [
        'h2'
      ],
      chatContentList: [
        '.chat-content-list'
      ],
      chatHeaderContent: [
        '.chat-header-content'
      ],
      promptItem: [
        '.chat-content-item.chat-content-item-user'
      ],
      promptContent: [
        '.user-content'
      ], // class="user-content"
      answerItem: [
        '.chat-content-item.chat-content-item-assistant'
      ],
      answerCompletion: [
        '.segment-assistant-actions-content'
      ],
      copyButton: [
        '.segment-assistant-actions-content .simple-button.size-small',
        '.assistant-actions .copy-button',
        '.response-actions .action-button',
        '[data-testid="copy-button"]',
        '.simple-button.size-small',
        '.copy-button',
        '.action-button',
        '[aria-label*="复制"]',
        '[aria-label*="Copy"]',
        '.segment-assistant-actions .simple-button',
        '.assistant-bottom-bar .copy-btn',
        '.kimi-copy-button',
        '.response-copy-button',
        '[data-action="copy"]',
        '.btn-copy',
        'button[title*="复制"]',
        'button[title*="Copy"]'
      ],
      markdown: [
        '.markdown'
      ]
}

