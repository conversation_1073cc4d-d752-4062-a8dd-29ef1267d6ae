import { SelectorConfig } from "./SelectorManager";


/**
 * Kimi 特定的选择器常量
 */
export const KimiSelectors = {
  // 聊天内容相关
  CHAT_CONTENT_LIST: '.chat-content-list',
  CHAT_HEADER_CONTENT: '.chat-header-content',
  
  // 问答节点
  QUESTION_ITEM: '.chat-content-item.chat-content-item-user',
  ANSWER_ITEM: '.chat-content-item.chat-content-item-assistant',
  
  // 答案完成状态
  ANSWER_COMPLETION: '.segment-assistant-actions-content',
  COPY_BUTTON: '.simple-button.size-small'
} as const;

/**
 * Kimi 适配器的选择器配置
 * 
 */
export const kimiSelector: SelectorConfig  = {
      inputField: [
        '[data-lexical-editor="true"]', // []包裹为属性选择器
        '.chat-input-editor[contenteditable="true"]',
        '.chat-input-editor', //.前缀为类选择器
        'textarea[placeholder*="请输入"]',
        'textarea[placeholder*="输入"]',
        'textarea[placeholder*="message"]',
        '[data-testid="chat-input"]',
        '.chat-input',
        '[contenteditable="true"]'
      ],
      sendButton: [
        '.send-button-container .send-button',  // 匹配所有状态的发送按钮
        '.send-button',                         // 通用发送按钮选择器
        '[data-testid="send-button"]',
        '[aria-label*="发送"]',
        '[aria-label*="Send"]',
        'button[aria-label*="发送"]',
        'button[aria-label*="Send"]',
        '.send-btn',
        '[data-testid*="send"]'
      ]

}

