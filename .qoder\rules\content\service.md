---
type: "development_rules"
description: "Content Script Service层开发规则"
---

# Content Script Service层开发规则

## Service层设计原则

### 职责定义
- **业务逻辑处理**: 处理复杂的业务规则和数据转换
- **跨模块共享**: 提供inject和capture模块共享的功能
- **外部通信**: 与Background、Database等外部服务通信
- **状态管理**: 管理复杂的业务状态和缓存

### 设计模式
- **单例模式**: 所有Service类继承Singleton基类
- **依赖注入**: 可以依赖Model、DatabaseProxy等其他服务
- **接口导向**: 提供清晰的公共接口方法
- **无状态设计**: Service方法应该是无状态的，状态由Model管理

## Service开发规范

### 基础结构要求
- **位置**: `extension/src/content/service/`
- **命名**: `ServiceName.ts`，如 `ArchiveService.ts`
- **继承**: 继承 `Singleton<ServiceName>` 基类
- **导出**: 使用 `export default class` 导出


### 方法设计规范
- **公共方法**: 使用 `public` 修饰符，提供给外部模块调用
- **私有方法**: 使用 `private` 修饰符，内部辅助方法
- **异步方法**: 复杂业务逻辑通常是异步的，返回 `Promise`
- **错误处理**: 统一的错误处理和日志记录

## Service使用规范

### 避免循环依赖
- Service A 依赖 Service B，则 Service B 不能依赖 Service A
- 如果出现循环依赖，考虑抽取共同逻辑到新的Service
- 使用Model作为数据共享的中介


### 日志规范
- 使用统一的日志前缀 `【Service】`
- 记录关键业务操作的开始和结果
- 记录错误信息和堆栈跟踪
- 避免记录敏感信息

## 最佳实践

### 接口设计
- 方法名称要清晰表达功能
- 参数类型要明确和完整
- 返回值要有明确的类型定义
- 提供详细的JSDoc注释

### 业务逻辑分层
- 简单逻辑：直接在Inject/Capture中处理
- 中等复杂度：抽取到Service方法
- 高复杂度：拆分为多个Service协作

### 代码组织
- 相关的业务逻辑放在同一个Service
- 单个Service文件不超过300行
- 复杂Service可以拆分为多个小Service
- 保持Service的单一职责原则